"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

View for ChiTietPhieuNhapHangBanTraLai (Customer Return Receipt Detail) model.
"""

from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import status, viewsets  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators.error_handling import api_exception_handler
from django_ledger.api.serializers.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai.chi_tiet_phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    ChiTietPhieuNhapHangBanTraLaiModelCreateUpdateSerializer,
    ChiTietPhieuNhapHangBanTraLaiModelSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    ChiTietPhieuNhapHangBanTraLaiModel,
)
from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    ChiTietPhieuNhapHangBanTraLaiService,
)


@extend_schema_view(
    list=extend_schema(
        summary='List Customer Return Receipt Details',
        description='Get a list of Customer Return Receipt Details for a specific Customer Return Receipt.',  # noqa: E501
        tags=['Sales - Customer Return Receipt Details'],
    ),
    retrieve=extend_schema(
        summary='Retrieve Customer Return Receipt Detail',
        description='Get a specific Customer Return Receipt Detail by UUID.',
        tags=['Sales - Customer Return Receipt Details'],
    ),
    create=extend_schema(
        summary='Create Customer Return Receipt Detail',
        description='Create a new Customer Return Receipt Detail for a specific Customer Return Receipt.',  # noqa: E501
        tags=['Sales - Customer Return Receipt Details'],
    ),
    update=extend_schema(
        summary='Update Customer Return Receipt Detail',
        description='Update a specific Customer Return Receipt Detail by UUID.',
        tags=['Sales - Customer Return Receipt Details'],
    ),
    partial_update=extend_schema(
        summary='Partially Update Customer Return Receipt Detail',
        description='Partially update a specific Customer Return Receipt Detail by UUID.',  # noqa: E501
        tags=['Sales - Customer Return Receipt Details'],
    ),
    destroy=extend_schema(
        summary='Delete Customer Return Receipt Detail',
        description='Delete a specific Customer Return Receipt Detail by UUID.',
        tags=['Sales - Customer Return Receipt Details'],
    ),
)
class ChiTietPhieuNhapHangBanTraLaiViewSet(EntityRelatedViewSet):
    """
    ViewSet for ChiTietPhieuNhapHangBanTraLai model.
    """

    pagination_class = ERPPagination  # noqa: F811
    permission_classes = [IsAuthenticated]
    serializer_class = (
        ChiTietPhieuNhapHangBanTraLaiModelSerializer  # noqa: F811
    )
    queryset = ChiTietPhieuNhapHangBanTraLaiModel.objects.all()

    def get_service(self):  # noqa: C901
        """
        Get the service instance.

        Returns
        -------
        ChiTietPhieuNhapHangBanTraLaiService
            The service instance.
        """
        return ChiTietPhieuNhapHangBanTraLaiService(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
        )

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the viewset.

        Returns
        -------
        QuerySet
            The queryset.
        """
        service = self.get_service()
        return service.get_queryset()

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List all details for a specific customer return receipt.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        phieu_nhap_uuid = kwargs.get('phieu_nhap_hang_ban_tra_lai_uuid')
        if not phieu_nhap_uuid:
            return Response(
                {'detail': 'Customer return receipt UUID is required.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        queryset = service.get_by_phieu_nhap(phieu_nhap_uuid)
        # Paginate results
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a specific customer return receipt detail.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        # Handle both 'uuid' and 'pk' parameter names from URL routing
        uuid_param = kwargs.get('uuid') or kwargs.get('pk')
        instance = service.get(uuid_param)
        if not instance:
            return Response(
                {'detail': 'Customer return receipt detail not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new customer return receipt detail.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        serializer = ChiTietPhieuNhapHangBanTraLaiModelCreateUpdateSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)
        # Create the instance
        instance = service.create(serializer.validated_data)
        # Return the created instance
        response_serializer = self.get_serializer(instance)
        return Response(
            response_serializer.data, status=status.HTTP_201_CREATED
        )

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a specific customer return receipt detail.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        instance = service.get(kwargs['uuid'])
        if not instance:
            return Response(
                {'detail': 'Customer return receipt detail not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = ChiTietPhieuNhapHangBanTraLaiModelCreateUpdateSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)
        # Update the instance
        instance = service.update(
            uuid=kwargs['uuid'], data=serializer.validated_data
        )
        # Return the updated instance
        response_serializer = self.get_serializer(instance)
        return Response(response_serializer.data, status=status.HTTP_200_OK)

    @api_exception_handler
    def partial_update(self, request, *args, **kwargs):  # noqa: C901
        """
        Partially update a specific customer return receipt detail.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        instance = service.get(kwargs['uuid'])
        if not instance:
            return Response(
                {'detail': 'Customer return receipt detail not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = ChiTietPhieuNhapHangBanTraLaiModelCreateUpdateSerializer(
            instance=instance, data=request.data, partial=True
        )
        serializer.is_valid(raise_exception=True)
        # Update the instance
        instance = service.update(
            uuid=kwargs['uuid'], data=serializer.validated_data
        )
        # Return the updated instance
        response_serializer = self.get_serializer(instance)
        return Response(response_serializer.data, status=status.HTTP_200_OK)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a specific customer return receipt detail.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        instance = service.get(kwargs['uuid'])
        if not instance:
            return Response(
                {'detail': 'Customer return receipt detail not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Delete the instance
        service.delete(kwargs['uuid'])

        return Response(status=status.HTTP_204_NO_CONTENT)
