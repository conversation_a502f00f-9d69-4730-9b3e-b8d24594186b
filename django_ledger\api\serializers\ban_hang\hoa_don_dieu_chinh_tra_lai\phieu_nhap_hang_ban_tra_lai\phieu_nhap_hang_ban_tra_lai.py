"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for PhieuNhapHangBanTraLai (Customer Return Receipt) model.
"""

from django.utils.translation import gettext_lazy as _  # noqa: F401
from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers._utils.chung_tu_fields import ChungTuSerializerMixin  # noqa: F401

from django_ledger.api.serializers.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai.chi_tiet_phieu_nhap_hang_ban_tra_lai import (  # noqa: F401
    ChiTietPhieuNhapHangBanTraLaiModelSerializer,
)
from django_ledger.api.serializers.danh_muc import (  # noqa: F401,
    NgoaiTeSerializer,
)
from django_ledger.api.serializers.nhan_vien import (  # noqa: F401,
    NhanVienModelSerializer,
)

from django_ledger.api.serializers.tax import (  # noqa: F401,
    TaxModelSerializer,
)
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    PhieuNhapHangBanTraLaiModel,
)


class PhieuNhapHangBanTraLaiModelSerializer(ChungTuSerializerMixin, serializers.ModelSerializer):
    """
    Serializer for PhieuNhapHangBanTraLai model.
    """

    # Read-only fields for related objects (ma_nk_data now handled by ChungTuSerializerMixin)
    ma_nv_data = serializers.SerializerMethodField(read_only=True)
    ma_nvbh_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    ma_dc_data = serializers.SerializerMethodField(read_only=True)
    ma_ptvc_data = serializers.SerializerMethodField(read_only=True)
    ma_ptgh_data = serializers.SerializerMethodField(read_only=True)
    ma_kh9_data = serializers.SerializerMethodField(read_only=True)
    ma_mau_ct_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet_items = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = PhieuNhapHangBanTraLaiModel
        fields = [
            'uuid',
            'entity_model',
            # ChungTu fields (handled by ChungTuSerializerMixin)
            'i_so_ct',
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            'chung_tu',
            'chung_tu_item',
            # Additional document info
            'dien_giai',
            'so_ct0',
            'so_ct2',
            'ngay_ct0',
            # Customer info
            'ma_kh',
            'ma_kh_data',
            'ma_so_thue',
            'ten_kh_thue',
            'ong_ba',
            'dia_chi',
            'e_mail',
            # Staff and unit info
            'ma_ngv',
            'ma_nv',
            'ma_nv_data',
            'ma_nvbh',
            'ma_nvbh_data',
            'tk',
            'tk_data',
            'unit_id',
            'unit_id_data',
            # Currency and payment
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'ma_thue',
            'ma_thue_data',
            'ma_pttt',
            # Status and flags
            'status',
            'transfer_yn',
            'invat_yn',
            'lo_yn',
            'vi_tri_yn',
            'qc_yn',
            # Electronic invoice
            'ma_tthddt',
            'so_ct_hddt',
            'ngay_ct_hddt',
            'so_ct2_hddt',
            'ma_mau_ct_hddt',
            # Additional info
            'ma_mau_bc',
            'ma_tc_thue',
            'ma_gd',
            'ghi_chu',
            'ly_do',
            'ma_mau_ct',
            'ma_mau_ct_data',
            'ma_tt',
            'ma_tt_data',
            'ma_dc',
            'ma_dc_data',
            'ma_ptvc',
            'ma_ptvc_data',
            'ma_ptgh',
            'ma_ptgh_data',
            'ma_kh9',
            'ma_kh9_data',
            # Totals
            't_so_luong',
            't_tien',
            't_tien_nt',
            't_tien_nt2',
            't_tien2',
            't_ck',
            't_ck_nt',
            't_tc_tien',
            't_tc_tien_nt2',
            't_km',
            't_km_nt',
            't_thue',
            't_thue_nt',
            't_tt',
            't_tt_nt',
            # Child data
            'chi_tiet_data',
            'chi_tiet_items',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'chung_tu',
            'chung_tu_item',
            'ma_nv_data',
            'ma_nvbh_data',
            'ma_nt_data',
            'ma_thue_data',
            'unit_id_data',
            'ma_kh_data',
            'tk_data',
            'ma_dc_data',
            'ma_ptvc_data',
            'ma_ptgh_data',
            'ma_kh9_data',
            'ma_mau_ct_data',
            'ma_tt_data',
            'chi_tiet_data',
            't_so_luong',
            't_tien',
            't_tien_nt',
            't_tien_nt2',
            't_tien2',
            't_ck',
            't_ck_nt',
            't_tc_tien',
            't_tc_tien_nt2',
            't_km',
            't_km_nt',
            't_thue',
            't_thue_nt',
            't_tt',
            't_tt_nt',
            'created',
            'updated',
        ]



    def get_ma_nv_data(self, obj):  # noqa: C901
        """
        Get ma_nv data.
        """
        if obj.ma_nv:
            return NhanVienModelSerializer(obj.ma_nv).data
        return None

    def get_ma_nvbh_data(self, obj):  # noqa: C901
        """
        Get ma_nvbh data.
        """
        if obj.ma_nvbh:
            return NhanVienModelSerializer(obj.ma_nvbh).data
        return None

    def get_ma_nt_data(self, obj):  # noqa: C901
        """
        Get ma_nt data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_thue_data(self, obj):  # noqa: C901
        """
        Get ma_thue data.
        """
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_unit_id_data(self, obj):  # noqa: C901
        """
        Get unit_id data.
        """
        if obj.unit_id:
            from django_ledger.api.serializers.unit import EntityUnitModelSerializer
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_chi_tiet_data(self, obj):  # noqa: C901
        """
        Get chi_tiet data.
        """
        chi_tiet_list = obj.chi_tiet_phieu_nhap_hang_ban_tra_lai.all()
        return ChiTietPhieuNhapHangBanTraLaiModelSerializer(
            chi_tiet_list, many=True
        ).data

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Get ma_kh data.
        """
        if obj.ma_kh:
            from django_ledger.api.serializers.customer import CustomerModelSerializer
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_data(self, obj):  # noqa: C901
        """
        Get tk data.
        """
        if obj.tk:
            from django_ledger.api.serializers.accounts import AccountModelSerializer
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_dc_data(self, obj):  # noqa: C901
        """
        Get ma_dc data.
        """
        if obj.ma_dc:
            from django_ledger.api.serializers.dia_chi import DiaChiSerializer
            return DiaChiSerializer(obj.ma_dc).data
        return None

    def get_ma_ptvc_data(self, obj):  # noqa: C901
        """
        Get ma_ptvc data.
        """
        if obj.ma_ptvc:
            from django_ledger.api.serializers.phuong_tien_van_chuyen import PhuongTienVanChuyenModelSerializer
            return PhuongTienVanChuyenModelSerializer(obj.ma_ptvc).data
        return None

    def get_ma_ptgh_data(self, obj):  # noqa: C901
        """
        Get ma_ptgh data.
        """
        if obj.ma_ptgh:
            from django_ledger.api.serializers.phuong_tien_giao_hang import PhuongTienGiaoHangModelSerializer
            return PhuongTienGiaoHangModelSerializer(obj.ma_ptgh).data
        return None

    def get_ma_kh9_data(self, obj):  # noqa: C901
        """
        Get ma_kh9 data.
        """
        if obj.ma_kh9:
            from django_ledger.api.serializers.customer import CustomerModelSerializer
            return CustomerModelSerializer(obj.ma_kh9).data
        return None

    def get_ma_mau_ct_data(self, obj):  # noqa: C901
        """
        Get ma_mau_ct data.
        """
        if obj.ma_mau_ct:
            from django_ledger.api.serializers.mau_so_hd import MauSoHDModelSerializer
            return MauSoHDModelSerializer(obj.ma_mau_ct).data
        return None

    def get_ma_tt_data(self, obj):  # noqa: C901
        """
        Get ma_tt data.
        """
        if obj.ma_tt:
            from django_ledger.api.serializers.han_thanh_toan import HanThanhToanModelSerializer
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None


class PhieuNhapHangBanTraLaiModelCreateUpdateSerializer(
    ChungTuSerializerMixin, serializers.ModelSerializer
):
    """
    Serializer for creating and updating PhieuNhapHangBanTraLai model.
    """

    chi_tiet_items = serializers.ListField(required=False, write_only=True)
    ma_unit = serializers.CharField(required=False, write_only=True, allow_blank=True)

    class Meta:
        model = PhieuNhapHangBanTraLaiModel
        fields = [
            # Document info
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            'dien_giai',
            'i_so_ct',
            'so_ct0',
            'so_ct2',
            'ngay_ct0',
            # Customer info
            'ma_kh',
            'ma_so_thue',
            'ten_kh_thue',
            'ong_ba',
            'dia_chi',
            'e_mail',
            # Staff and unit info
            'ma_ngv',
            'ma_nv',
            'ma_nvbh',
            'tk',
            'unit_id',
            'ma_unit',
            # Currency and payment
            'ma_nt',
            'ty_gia',
            'ma_thue',
            'ma_pttt',
            # Status and flags
            'status',
            'transfer_yn',
            'invat_yn',
            'lo_yn',
            'vi_tri_yn',
            'qc_yn',
            # Electronic invoice
            'ma_tthddt',
            'so_ct_hddt',
            'ngay_ct_hddt',
            'so_ct2_hddt',
            'ma_mau_ct_hddt',
            # Additional info
            'ma_mau_bc',
            'ma_tc_thue',
            'ma_gd',
            'ghi_chu',
            'ly_do',
            'ma_mau_ct',
            'ma_tt',
            'ma_dc',
            'ma_ptvc',
            'ma_ptgh',
            'ma_kh9',
            # Totals
            't_so_luong',
            't_tien',
            't_tien_nt',
            't_tien_nt2',
            't_tien2',
            't_ck',
            't_ck_nt',
            't_tc_tien',
            't_tc_tien_nt2',
            't_km',
            't_km_nt',
            't_thue',
            't_thue_nt',
            't_tt',
            't_tt_nt',
            # Child data
            'chi_tiet_items',
        ]

    def validate(self, data):
        """
        Validate the serializer data.
        This method calls ChungTu mixin validation for so_ct and i_so_ct.
        """
        # Use ChungTu mixin for validation
        data = self.validate_chung_tu_fields(data)

        return data
