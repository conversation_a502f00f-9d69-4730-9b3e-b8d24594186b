#!/bin/bash

# Test incremental API for phieu_nhap_hang_ban_tra_lai
# Start with basic fields and add more to find the problematic field

echo "Testing with basic fields + ma_kh..."
curl -X POST 'http://127.0.0.1:8000/api/entities/tutimi-dnus2xnc/erp/ban-hang/hoa-don-dieu-chinh-tra-lai/phieu-nhap-hang-ban-tra-lai/' \
--header 'accept: application/json' \
--header 'authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
--header 'content-type: application/json' \
--data-raw '{
"ten_kh_thue": "Test Customer",
"ong_ba": "<PERSON> Doe",
"dia_chi": "456 Updated Street",
"e_mail": "<EMAIL>",
"tk": "53fb8196-b7c5-4290-b32b-3a00a2869519",
"dien_giai": "This is a test",
"ma_unit": "",
"unit_id": "9b8fca4b-1621-414c-881a-c4f01624f2da",
"so_ct": "BC1.07.25.000003",
"ma_nk": "e15879f1-c472-40f4-9bb4-ede4683a4057",
"ngay_ct": "2025-07-01",
"ngay_lct": "2025-07-01",
"so_ct0": "zxczxc",
"so_ct2": "zxczxcz",
"ngay_ct0": "2025-07-01",
"ma_nt": "09d90e95-163c-4bbe-814d-8f1b8dddf4bd",
"ty_gia": "1.00",
"status": "3",
"transfer_yn": false,
"i_so_ct": 3,
"t_tien_nt": 121,
"t_tien": 12,
"t_ck_nt": 12,
"t_ck": 12,
"t_tc_tien_nt2": 12,
"t_tc_tien": 12,
"t_km_nt": 12,
"t_km": 12,
"ma_kh": "82946c13-4542-4fdd-abd6-1d417b70f851",
"ma_so_thue": "TAX-UPDATED",
"ma_nvbh": "ed3d53b1-2133-4b4d-91ae-ff5fcad96982",
"ma_mau_ct": "710c2268-c6bb-41f4-bcb1-760b96866665",
"ma_tt": "c360ae11-062b-4edf-ac9f-800f130e809a",
"ma_dc": "a4afcd61-8b5a-491a-835a-329d3d94b035",
"chi_tiet_items": [
{
"line": 1,
"ma_vt": "98fa3252-d27b-4a76-9a8b-c06a05b4dd2e",
"ten_vt": "Gao Nep",
"dvt": "e8f079b0-0455-4488-854a-5cb59c8dd1f0",
"ma_kho": "ecc96534-8eff-46b8-8606-6d9336d2a82e",
"so_luong": 13,
"gia_nt": 13,
"tien_nt": 1337
}
]
}' -s | jq '.'
