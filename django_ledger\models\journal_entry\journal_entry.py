"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

JournalEntryModel
------------
The JournalEntryModel is a fundamental component of the Django Ledger system, responsible for recording and managing  # noqa: E501
accounting transactions in the system. Each journal entry represents a financial transaction that affects multiple  # noqa: E501
accounts through debits and credits.

Double Entry Accounting
-----------------------
The JournalEntryModel implements double entry accounting principles:
- Each transaction must have at least one debit and one credit entry
- The total debits must equal the total credits
- Each entry must be associated with a valid account
- Each entry must have a valid date and reference number

Transaction Types
----------------
Journal entries can be of different types:
1. Regular journal entries
2. Adjusting entries
3. Closing entries
4. Reversing entries

Each type serves a specific purpose in the accounting cycle.
"""

from datetime import date, datetime  # noqa: F401,
from decimal import Decimal  # noqa: F401,
from enum import Enum  # noqa: F401,
from itertools import chain  # noqa: F401,
from typing import Dict, List, Optional, Set, Tuple, Union  # noqa: F401,
from uuid import UUID, uuid4  # noqa: F401,

from django.core.exceptions import (  # noqa: F401,
    FieldError,
    ObjectDoesNotExist,
    ValidationError,
)
from django.db import IntegrityError, models, transaction  # noqa: F401,
from django.db.models import Count, F, Manager, Q, QuerySet, Sum  # noqa: F401,
from django.db.models.functions import Coalesce  # noqa: F401,
from django.db.models.signals import pre_save  # noqa: F401,
from django.urls import reverse  # noqa: F401,
from django.utils.timezone import localtime  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.io import roles  # noqa: F401,
from django_ledger.io.io_core import get_localtime  # noqa: F401,
from django_ledger.models.entity import EntityModel, EntityStateModel  # noqa: F401,
from django_ledger.models.ledger import LedgerModel  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn
from django_ledger.models.transactions import (  # noqa: F401,
    TransactionModel,
    TransactionModelQuerySet,
)
from django_ledger.models.utils import lazy_loader  # noqa: F401,

from .exceptions import JournalEntryValidationError  # noqa: F401,
from .methods import (  # noqa: F401,
    JournalEntryActivityMixin,
    JournalEntryBalanceMixin,
    JournalEntryStateMixin,
    JournalEntryTransactionMixin,
    JournalEntryUrlMixin,
)
from .methods.core import JournalEntryCoreMixin  # noqa: F401,
from .methods.entity import JournalEntryEntityMixin  # noqa: F401,
from .methods.properties import JournalEntryPropertyMixin  # noqa: F401,


class JournalEntryModelQuerySet(QuerySet):
    """
    A custom QuerySet for working with Journal Entry models, providing additional
    convenience methods and validations for specific use cases.
    """

    def create(  # noqa: C901
        self,
        verify_on_save: bool = False,
        force_create: bool = False,
        **kwargs,
    ):
        """
        Creates a new Journal Entry while enforcing business logic validations.
        """
        is_posted = kwargs.get("posted")
        if is_posted and not force_create:
            raise FieldError(
                "Cannot create Journal Entries in a posted state without 'force_create=True'."
            )  # noqa: E501

        obj = self.model(**kwargs)
        self._for_write = True
        # Bypass custom save method to avoid parameter conflicts
        # Call Django's default Model.save() directly
        if verify_on_save:
            # If verification is needed, use custom save method
            obj.save(verify=True, force_insert=True, using=self.db)
        else:
            # If no verification needed, use Django's default save
            super(type(obj), obj).save(force_insert=True, using=self.db)
        return obj

    def posted(self):  # noqa: C901
        """Filters the QuerySet to include only posted Journal Entries."""
        return self.filter(posted=True)

    def unposted(self):  # noqa: C901
        """Filters the QuerySet to include only unposted Journal Entries."""
        return self.filter(posted=False)

    def locked(self):  # noqa: C901
        """Filters the QuerySet to include only locked Journal Entries."""
        return self.filter(locked=True)

    def unlocked(self):  # noqa: C901
        """Filters the QuerySet to include only unlocked Journal Entries."""
        return self.filter(locked=False)

    def for_ledger(self, ledger_pk: Union[str, UUID, LedgerModel]):  # noqa: C901
        """Filters the QuerySet to include Journal Entries associated with a specific Ledger."""  # noqa: E501
        if isinstance(ledger_pk, LedgerModel):
            return self.filter(ledger=ledger_pk)
        return self.filter(ledger__uuid__exact=ledger_pk)

    def active(self) -> QuerySet:  # noqa: C901
        """Active journal entries that are currently valid."""
        return self.filter(status__exact="1")

    def for_entity(self, entity_slug: str, user_model):  # noqa: C901
        """Returns a QuerySet of JournalEntryModels that are associated with a specific EntityModel & UserModel."""  # noqa: E501
        return self.filter(
            Q(entity_model__slug__exact=entity_slug) & Q(entity_model__admin=user_model)
            | Q(entity_model__managers__in=[user_model])
        )

    def for_period(self, start_date, end_date) -> QuerySet:  # noqa: C901
        """Journal entries within a specific date range."""
        return self.filter(ngay_ct__range=[start_date, end_date])

    def by_journal_type(self, journal_type: str) -> QuerySet:  # noqa: C901
        """Filter journal entries by journal type."""
        return self.filter(journal_type=journal_type)




class JournalEntryModelManager(Manager):
    """
    A custom manager for the JournalEntryModel that extends Django's default
    Manager with additional query features.
    """

    def get_queryset(self) -> JournalEntryModelQuerySet:  # noqa: C901
        """Returns the default queryset for JournalEntryModel with additional annotations."""  # noqa: E501
        qs = JournalEntryModelQuerySet(self.model, using=self._db)
        return qs.annotate(
            _entity_uuid=F("ledger__entity_id"),
            _entity_slug=F("ledger__entity__slug"),
            _entity_last_closing_date=F("ledger__entity__last_closing_date"),
            _ledger_is_locked=F("ledger__locked"),
            txs_count=Count("transactionmodel"),
        )

    def for_user(self, user_model) -> JournalEntryModelQuerySet:  # noqa: C901
        """Filters the JournalEntryModel queryset for the given user."""
        qs = self.get_queryset()
        if user_model.is_superuser:
            return qs

        return qs.filter(
            Q(ledger__entity__admin=user_model)
            | Q(ledger__entity__managers__in=[user_model])
        )

    def for_entity(
        self, entity_slug: Union[str, EntityModel], user_model
    ) -> JournalEntryModelQuerySet:  # noqa: C901
        """Filters the JournalEntryModel queryset for a specific entity and user."""
        qs = self.for_user(user_model)
        if isinstance(entity_slug, EntityModel):
            return qs.filter(ledger__entity=entity_slug)
        return qs.filter(ledger__entity__slug__iexact=entity_slug)


class ActivityEnum(Enum):
    """Represents the database prefixes used for different types of accounting activities."""  # noqa: E501

    OPERATING = "op"
    INVESTING = "inv"
    FINANCING = "fin"


class JournalEntryModelAbstract(
    CreateUpdateMixIn,
    JournalEntryActivityMixin,
    JournalEntryBalanceMixin,
    JournalEntryStateMixin,
    JournalEntryTransactionMixin,
    JournalEntryUrlMixin,
    JournalEntryCoreMixin,
    JournalEntryEntityMixin,
    JournalEntryPropertyMixin,
):
    # Reference to the validation error class for use in mixins
    JournalEntryValidationError = JournalEntryValidationError
    """
    Abstract base model for handling journal entries in the bookkeeping system.
    """

    # Constants for activity types
    OPERATING_ACTIVITY = ActivityEnum.OPERATING.value
    FINANCING_OTHER = ActivityEnum.FINANCING.value
    INVESTING_OTHER = ActivityEnum.INVESTING.value
    INVESTING_SECURITIES = f"{ActivityEnum.INVESTING.value}_securities"
    INVESTING_PPE = f"{ActivityEnum.INVESTING.value}_ppe"
    FINANCING_STD = f"{ActivityEnum.FINANCING.value}_std"
    FINANCING_LTD = f"{ActivityEnum.FINANCING.value}_ltd"
    FINANCING_EQUITY = f"{ActivityEnum.FINANCING.value}_equity"
    FINANCING_DIVIDENDS = f"{ActivityEnum.FINANCING.value}_dividends"
    # Activity categories for dropdown
    ACTIVITIES = [
        (_("Operating"), ((OPERATING_ACTIVITY, _("Operating")),)),
        (
            _("Investing"),
            (
                (INVESTING_PPE, _("Purchase/Disposition of PPE")),
                (INVESTING_SECURITIES, _("Purchase/Disposition of Securities")),
                (INVESTING_OTHER, _("Investing Activity Other")),
            ),
        ),
        (
            _("Financing"),
            (
                (FINANCING_STD, _("Payoff of Short Term Debt")),
                (FINANCING_LTD, _("Payoff of Long Term Debt")),
                (
                    FINANCING_EQUITY,
                    _(
                        "Issuance of Common Stock, Preferred Stock or Capital Contribution"
                    ),  # noqa: E501
                ),
                (
                    FINANCING_DIVIDENDS,
                    _("Dividends or Distributions to Shareholders"),
                ),
                (FINANCING_OTHER, _("Financing Activity Other")),
            ),
        ),
    ]

    # Utility mappings for activity validation
    VALID_ACTIVITIES = list(
        chain.from_iterable([[a[0] for a in cat[1]] for cat in ACTIVITIES])
    )
    MAP_ACTIVITIES = dict(
        chain.from_iterable([[(a[0], cat[0]) for a in cat[1]] for cat in ACTIVITIES])
    )
    NON_OPERATIONAL_ACTIVITIES = [
        a for a in VALID_ACTIVITIES if ActivityEnum.OPERATING.value not in a
    ]

    # Field definitions
    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)
    je_number = models.SlugField(
        max_length=25,
        editable=False,
        verbose_name=_("Journal Entry Number"),
    )
    timestamp = models.DateTimeField(verbose_name=_("Timestamp"), default=localtime)
    description = models.CharField(
        max_length=70,
        blank=True,
        null=True,
        verbose_name=_("Description"),
    )
    entity_unit = models.ForeignKey(
        "django_ledger.EntityUnitModel",
        on_delete=models.RESTRICT,
        blank=True,
        null=True,
        verbose_name=_("Associated Entity Unit"),
    )
    activity = models.CharField(
        choices=ACTIVITIES,
        max_length=20,
        null=True,
        blank=True,
        editable=False,
        verbose_name=_("Activity"),
    )
    origin = models.CharField(
        max_length=30, blank=True, null=True, verbose_name=_("Origin")
    )

    # Định nghĩa choices cho loại bút toán theo chuẩn ERP Việt Nam
    JOURNAL_TYPE_CONGNO = 'CONGNO'
    JOURNAL_TYPE_DT0CK = 'DT0CK'
    JOURNAL_TYPE_DTCK = 'DTCK'
    JOURNAL_TYPE_THUE = 'THUE'
    JOURNAL_TYPE_THUHD = 'THUHD'
    JOURNAL_TYPE_THUKH = 'THUKH'
    JOURNAL_TYPE_CHIHD = 'CHIHD'
    JOURNAL_TYPE_CHINCC = 'CHINCC'
    JOURNAL_TYPE_CHITHUE = 'CHITHUE'
    JOURNAL_TYPE_TONKHO = 'TONKHO'
    JOURNAL_TYPE_KHAC = 'KHAC'
    JOURNAL_TYPE_CK = 'CK'

    JOURNAL_ENTRY_TYPES = [
        (JOURNAL_TYPE_CONGNO, _('Bút toán công nợ')),
        (JOURNAL_TYPE_DT0CK, _('Doanh thu không có chiết khấu')),
        (JOURNAL_TYPE_DTCK, _('Doanh thu có chiết khấu')),
        (JOURNAL_TYPE_THUE, _('Bút toán thuế')),
        (JOURNAL_TYPE_THUHD, _('Thu từ hóa đơn')),
        (JOURNAL_TYPE_THUKH, _('Thu từ khách hàng')),
        (JOURNAL_TYPE_CHIHD, _('Chi hóa đơn')),
        (JOURNAL_TYPE_CHINCC, _('Chi nhà cung cấp')),
        (JOURNAL_TYPE_CHITHUE, _('Chi thuế')),
        (JOURNAL_TYPE_TONKHO, _('Bút toán tồn kho')),
        (JOURNAL_TYPE_KHAC, _('Loại khác')),
        (JOURNAL_TYPE_CK, _('Chiết khấu')),
    ]

    journal_type = models.CharField(
        max_length=20,
        choices=JOURNAL_ENTRY_TYPES,
        blank=True,
        null=True,
        verbose_name=_("Loại bút toán"),
        help_text=_("Phân loại theo tính chất nghiệp vụ kế toán")
    )
    posted = models.BooleanField(default=False, verbose_name=_("Posted"))
    locked = models.BooleanField(default=False, verbose_name=_("Locked"))
    is_closing_entry = models.BooleanField(default=False)
    ledger = models.ForeignKey(
        "django_ledger.LedgerModel",
        verbose_name=_("Ledger"),
        related_name="journal_entries",
        on_delete=models.CASCADE,
    )

    # Customer relationship for debt calculation (unified customer/vendor model)
    customer = models.ForeignKey(
        "django_ledger.CustomerModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Customer"),
        help_text=_(
            "Customer or vendor (via is_vendor field) associated with this journal entry for debt tracking"
        ),
        related_name="journal_entries",
    )

    # OneToOne relationship with ChungTuItem
    chung_tu_item = models.OneToOneField(
        'django_ledger.ChungTuItemModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Document Item'),
        related_name='journal_entry',
    )

    ma_ngv = models.CharField(max_length=50, null=True, blank=True, verbose_name="Mã người giao việc")
    dien_giai = models.CharField(max_length=255, verbose_name="Diễn giải")
    ma_nt = models.CharField(max_length=10, verbose_name="Mã ngoại tệ")
    ty_gia = models.DecimalField(max_digits=18, decimal_places=2, verbose_name="Tỷ giá")
    status = models.CharField(max_length=2, verbose_name="Trạng thái")
    transfer_yn = models.BooleanField(default=False, verbose_name="Đã chuyển")
    t_ps_no_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name="Tổng phát sinh nợ ngoại tệ",
    )
    t_ps_no = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name="Tổng phát sinh nợ"
    )
    t_ps_co_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name="Tổng phát sinh có ngoại tệ",
    )
    t_ps_co = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name="Tổng phát sinh có"
    )

    # Custom manager
    objects = JournalEntryModelManager.from_queryset(
        queryset_class=JournalEntryModelQuerySet
    )()

    class Meta:
        abstract = True
        ordering = ["-created"]
        verbose_name = _("Journal Entry")
        verbose_name_plural = _("Journal Entries")
        indexes = [
            models.Index(fields=["ledger"]),
            models.Index(fields=["timestamp"]),
            models.Index(fields=["activity"]),
            models.Index(fields=["entity_unit"]),
            models.Index(fields=["locked"]),
            models.Index(fields=["posted"]),
            models.Index(fields=["je_number"]),
            models.Index(fields=["is_closing_entry"]),
            # Indexes for debt calculation performance
            models.Index(fields=["customer", "timestamp"]),
            models.Index(fields=["ledger", "customer"]),
            # Index for journal type filtering and reporting
            models.Index(fields=["journal_type"]),
            models.Index(fields=["journal_type", "timestamp"]),
            models.Index(fields=["ledger", "journal_type"]),
        ]

    def __init__(self, *args, **kwargs):  # noqa: C901
        super().__init__(*args, **kwargs)
        self._verified = False

    # Properties to access ChungTuItem fields
    @property
    def i_so_ct(self):
        """Get document number sequence from chung_tu_item."""
        return self.chung_tu_item.i_so_ct if self.chung_tu_item else None

    @property
    def ma_nk(self):
        """Get document series code from chung_tu_item."""
        return self.chung_tu_item.ma_nk if self.chung_tu_item else None

    @property
    def so_ct(self):
        """Get document number from chung_tu_item."""
        return self.chung_tu_item.so_ct if self.chung_tu_item else None

    @property
    def ngay_ct(self):
        """Get document date from chung_tu_item."""
        return self.chung_tu_item.ngay_ct if self.chung_tu_item else None

    @property
    def ngay_lct(self):
        """Get document creation date from chung_tu_item."""
        return self.chung_tu_item.ngay_lct if self.chung_tu_item else None


class JournalEntryModel(JournalEntryModelAbstract):
    """
    Base JournalEntryModel from Abstract.
    """

    class Meta(JournalEntryModelAbstract.Meta):
        abstract = False
        db_table = "journal_entry"


def journalentrymodel_presave(instance: JournalEntryModel, **kwargs):  # noqa: C901
    if instance._state.adding:
        # cannot add journal entries to a locked ledger...
        if (
            hasattr(instance, 'ledger')
            and instance.ledger
            and hasattr(instance.ledger, 'locked')
            and instance.ledger.locked
        ):
            raise JournalEntryValidationError(
                message=_(
                    f"Cannot add Journal Entries to locked LedgerModel {instance.ledger_id}"
                )  # noqa: E501
            )

    # Generate journal entry number if method exists
    if hasattr(instance, 'generate_je_number'):
        instance.generate_je_number(commit=False)


pre_save.connect(journalentrymodel_presave, sender=JournalEntryModel)
