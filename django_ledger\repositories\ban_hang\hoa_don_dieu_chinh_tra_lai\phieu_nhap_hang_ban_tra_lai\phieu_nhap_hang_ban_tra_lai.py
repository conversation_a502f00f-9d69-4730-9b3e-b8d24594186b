"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for PhieuNhapHangBanTraLai (Customer Return Receipt) model.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import connection
from django.db.models import Q, QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

# Import the EntityModel
from django_ledger.models import EntityModel  # noqa: F401,
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    PhieuNhapHangBanTraLaiModel,
)
from django_ledger.repositories._utils.chung_tu_item_utils import (
    process_chung_tu_fields_extraction_and_conversion,
    update_instance_with_chung_tu_fields,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class PhieuNhapHangBanTraLaiRepository(BaseRepository):
    """
    Repository class for handling PhieuNhapHangBanTraLai model database operations.
    Implements the Repository pattern for PhieuNhapHangBanTraLai.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the PhieuNhapHangBanTraLaiModel.
        """
        super().__init__(model_class=PhieuNhapHangBanTraLaiModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for PhieuNhapHangBanTraLaiModel.

        Returns
        -------
        QuerySet
            The base queryset for PhieuNhapHangBanTraLaiModel.
        """
        return self.model_class.objects.all().select_related(
            'entity_model',
            'chung_tu_item__ma_nk',
            'ma_nv',
            'ma_nt',
            'ma_thue',
            'unit_id',
            'ma_kh',
            'tk',
            'ma_dc',
            'ma_ptvc',
            'ma_ptgh',
            'ma_kh9',
            'ma_mau_ct',
            'ma_tt',
        )

    def get_for_entity(self, entity_slug: str, user_model) -> QuerySet:  # noqa: C901
        """
        Get a queryset of PhieuNhapHangBanTraLaiModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        user_model : UserModel
            The user model

        Returns
        -------
        QuerySet
            A queryset of PhieuNhapHangBanTraLaiModel instances
        """
        return self.model_class.objects.for_entity(
            entity_slug=entity_slug, user_model=user_model
        )

    def get_by_uuid(
        self, uuid: UUID
    ) -> Optional[PhieuNhapHangBanTraLaiModel]:  # noqa: C901
        """
        Get a PhieuNhapHangBanTraLaiModel instance by UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the PhieuNhapHangBanTraLaiModel

        Returns
        -------
        Optional[PhieuNhapHangBanTraLaiModel]
            The PhieuNhapHangBanTraLaiModel instance if found, None otherwise
        """
        try:
            return self.model_class.objects.get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str, user_model, **kwargs) -> QuerySet:  # noqa: C901
        """
        Get a list of PhieuNhapHangBanTraLaiModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        user_model : UserModel
            The user model
        **kwargs : dict
            Additional filter parameters

        Returns
        -------
        QuerySet
            A queryset of PhieuNhapHangBanTraLaiModel instances
        """
        qs = self.get_for_entity(entity_slug=entity_slug, user_model=user_model)
        # Apply search if provided
        search_query = kwargs.get('search_query')
        if search_query:
            qs = qs.filter(
                Q(so_ct__icontains=search_query)
                | Q(ma_kh__icontains=search_query)
                | Q(ten_kh_thue__icontains=search_query)
                | Q(dien_giai__icontains=search_query)
            )

        # Apply status filter if provided
        status = kwargs.get('status')
        if status:
            qs = qs.filter(status=status)
        # Apply date range filter if provided
        from_date = kwargs.get('from_date')
        to_date = kwargs.get('to_date')
        if from_date and to_date:
            qs = qs.filter(chung_tu_item__ngay_ct__range=[from_date, to_date])
        elif from_date:
            qs = qs.filter(chung_tu_item__ngay_ct__gte=from_date)
        elif to_date:
            qs = qs.filter(chung_tu_item__ngay_ct__lte=to_date)
        # Apply ordering
        return qs.order_by('-chung_tu_item__ngay_ct', '-chung_tu_item__so_ct')

    # Use the convert_uuids_to_model_instances method from BaseRepository

    def create(  # noqa: C901
        self,
        entity_slug: str,
        user_model=None,
        data: Dict[str, Any] = None,
    ) -> PhieuNhapHangBanTraLaiModel:
        """
        Create a new customer return receipt

        Parameters
        ----------
        entity_slug : str
            The entity slug
        user_model : UserModel
            The user model
        data : Dict[str, Any]
            The data to create the customer return receipt with

        Returns
        -------
        PhieuNhapHangBanTraLaiModel
            The created customer return receipt
        """
        entity_model = get_object_or_404(EntityModel, slug=entity_slug)

        # Use utility function to extract and process ChungTu fields
        chung_tu_fields, data_copy = process_chung_tu_fields_extraction_and_conversion(
            data
        )

        # Convert UUID strings to model instances for remaining data (including chung_tu)
        processed_data_copy = self.convert_uuids_to_model_instances(data_copy)

        # Create the instance without ChungTu fields first
        instance = self.model_class(entity_model=entity_model, **processed_data_copy)

        # Set ChungTu fields using property setters
        for field_name, value in chung_tu_fields.items():
            setattr(instance, field_name, value)

        # Save the instance
        instance.save()

        return instance



    def update(
        self, uuid: UUID, data: Dict[str, Any]
    ) -> PhieuNhapHangBanTraLaiModel:  # noqa: C901
        """
        Update a customer return receipt

        Parameters
        ----------
        uuid : UUID
            The UUID of the customer return receipt
        data : Dict[str, Any]
            The data to update the customer return receipt with

        Returns
        -------
        PhieuNhapHangBanTraLaiModel
            The updated customer return receipt
        """
        # Get the instance
        instance = self.get_by_uuid(uuid)
        if not instance:
            raise self.model_class.DoesNotExist(
                f"PhieuNhapHangBanTraLaiModel with UUID {uuid} does not exist"
            )

        # Use utility function to handle ChungTu field updates
        return update_instance_with_chung_tu_fields(
            instance=instance,
            data=data,
            convert_uuids_func=self.convert_uuids_to_model_instances,
        )

    def delete(self, uuid: UUID) -> bool:  # noqa: C901
        """
        Delete a customer return receipt

        Parameters
        ----------
        uuid : UUID
            The UUID of the customer return receipt

        Returns
        -------
        bool
            True if the customer return receipt was deleted, False otherwise
        """
        instance = self.get_by_uuid(uuid)
        if not instance:
            return False

        instance.delete()
        return True

    def execute_raw_query(
        self, query: str, params: List[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Execute raw SQL query for sales return summary report.

        This method follows the CCDC allocation pattern for raw SQL execution.
        Repository only executes the query, does not build it.

        Parameters
        ----------
        query : str
            The raw SQL query to execute
        params : List[Any], optional
            Query parameters, by default None

        Returns
        -------
        List[Dict[str, Any]]
            Raw query results as list of dictionaries
        """
        if params is None:
            params = []

        try:
            with connection.cursor() as cursor:
                cursor.execute(query, params)
                columns = [col[0] for col in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return results

        except Exception as e:
            raise