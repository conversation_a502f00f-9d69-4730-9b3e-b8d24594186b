"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

View for PhieuNhapHangBanTraLai (Customer Return Receipt) model.
"""

from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import status, viewsets  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401,

from django_ledger.api.decorators.error_handling import api_exception_handler
from django_ledger.api.serializers.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    PhieuNhapHangBanTraLaiModelCreateUpdateSerializer,
    PhieuNhapHangBanTraLaiModelSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    PhieuNhapHangBanTraLaiModel,
)
from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    PhieuNhapHangBanTraLaiService,
)


@extend_schema_view(
    list=extend_schema(
        summary='List Customer Return Receipts',
        description='Get a list of Customer Return Receipts for a specific entity.',
        tags=['Sales - Customer Return Receipts'],
    ),
    retrieve=extend_schema(
        summary='Retrieve Customer Return Receipt',
        description='Get a specific Customer Return Receipt by UUID.',
        tags=['Sales - Customer Return Receipts'],
    ),
    create=extend_schema(
        summary='Create Customer Return Receipt',
        description='Create a new Customer Return Receipt for a specific entity.',
        tags=['Sales - Customer Return Receipts'],
    ),
    update=extend_schema(
        summary='Update Customer Return Receipt',
        description='Update a specific Customer Return Receipt by UUID.',
        tags=['Sales - Customer Return Receipts'],
    ),
    partial_update=extend_schema(
        summary='Partially Update Customer Return Receipt',
        description='Partially update a specific Customer Return Receipt by UUID.',
        tags=['Sales - Customer Return Receipts'],
    ),
    destroy=extend_schema(
        summary='Delete Customer Return Receipt',
        description='Delete a specific Customer Return Receipt by UUID.',
        tags=['Sales - Customer Return Receipts'],
    ),
    chi_tiet=extend_schema(
        summary='Get Customer Return Receipt Details',
        description='Get all details for a specific Customer Return Receipt by UUID.',
        tags=['Sales - Customer Return Receipts'],
    ),
)
class PhieuNhapHangBanTraLaiViewSet(EntityRelatedViewSet):
    """
    ViewSet for PhieuNhapHangBanTraLai model.
    """

    pagination_class = ERPPagination  # noqa: F811
    permission_classes = [IsAuthenticated]
    serializer_class = PhieuNhapHangBanTraLaiModelSerializer  # noqa: F811
    queryset = PhieuNhapHangBanTraLaiModel.objects.all()

    def get_service(self):  # noqa: C901
        """
        Get the service instance.

        Returns
        -------
        PhieuNhapHangBanTraLaiService
            The service instance.
        """
        return PhieuNhapHangBanTraLaiService(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
        )

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the viewset.

        Returns
        -------
        QuerySet
            The queryset.
        """
        service = self.get_service()
        return service.get_queryset()

    @api_exception_handler
    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List all customer return receipts for a specific entity.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        # Get query parameters
        search_query = request.query_params.get('search')
        status = request.query_params.get('status')
        from_date = request.query_params.get('from_date')
        to_date = request.query_params.get('to_date')
        # Get data from service
        queryset = service.list(
            search_query=search_query,
            status=status,
            from_date=from_date,
            to_date=to_date,
        )

        # Paginate results
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @api_exception_handler
    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a specific customer return receipt.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        # Handle both 'uuid' and 'pk' parameter names from URL routing
        uuid_param = kwargs.get('uuid') or kwargs.get('pk')
        instance = service.get(uuid_param)
        if not instance:
            return Response(
                {'detail': 'Customer return receipt not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)
    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new customer return receipt.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        serializer = PhieuNhapHangBanTraLaiModelCreateUpdateSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)

        # Extract chi_tiet_items if present
        chi_tiet_items = serializer.validated_data.pop('chi_tiet_items', None)

        # Debug logging
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Extracted chi_tiet_items: {chi_tiet_items}")
        if chi_tiet_items:
            logger.info(f"Number of chi_tiet_items: {len(chi_tiet_items)}")

        # Create the instance
        instance = service.create(
            data=serializer.validated_data, chi_tiet_list=chi_tiet_items
        )

        # Return the created instance
        response_serializer = self.get_serializer(instance)
        return Response(
            response_serializer.data, status=status.HTTP_201_CREATED
        )
    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a specific customer return receipt.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        # Handle both 'uuid' and 'pk' parameter names from URL routing
        uuid_param = kwargs.get('uuid') or kwargs.get('pk')
        instance = service.get(uuid_param)
        if not instance:
            return Response(
                {'detail': 'Customer return receipt not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = PhieuNhapHangBanTraLaiModelCreateUpdateSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)
        # Extract chi_tiet_items if present
        chi_tiet_items = serializer.validated_data.pop('chi_tiet_items', None)
        # Update the instance
        instance = service.update(
            uuid=uuid_param, data=serializer.validated_data
        )
        # Return the updated instance
        response_serializer = self.get_serializer(instance)
        return Response(response_serializer.data, status=status.HTTP_200_OK)
    @api_exception_handler
    def partial_update(self, request, *args, **kwargs):  # noqa: C901
        """
        Partially update a specific customer return receipt.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        # Handle both 'uuid' and 'pk' parameter names from URL routing
        uuid_param = kwargs.get('uuid') or kwargs.get('pk')
        instance = service.get(uuid_param)
        if not instance:
            return Response(
                {'detail': 'Customer return receipt not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = PhieuNhapHangBanTraLaiModelCreateUpdateSerializer(
            instance=instance, data=request.data, partial=True
        )
        serializer.is_valid(raise_exception=True)
        # Extract chi_tiet_items if present
        chi_tiet_items = serializer.validated_data.pop('chi_tiet_items', None)
        # Update the instance
        instance = service.update(
            uuid=uuid_param, data=serializer.validated_data
        )
        # Return the updated instance
        response_serializer = self.get_serializer(instance)
        return Response(response_serializer.data, status=status.HTTP_200_OK)
    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a specific customer return receipt.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        service = self.get_service()
        # Handle both 'uuid' and 'pk' parameter names from URL routing
        uuid_param = kwargs.get('uuid') or kwargs.get('pk')
        instance = service.get(uuid_param)
        if not instance:
            return Response(
                {'detail': 'Customer return receipt not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Delete the instance
        service.delete(uuid_param)

        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['get'])
    def chi_tiet(self, request, *args, **kwargs):  # noqa: C901
        """
        Get all details for a specific customer return receipt.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        from django_ledger.api.serializers.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai.chi_tiet_phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
            ChiTietPhieuNhapHangBanTraLaiModelSerializer,
        )
        from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
            ChiTietPhieuNhapHangBanTraLaiService,
        )

        chi_tiet_service = ChiTietPhieuNhapHangBanTraLaiService(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
        )
        # Handle both 'uuid' and 'pk' parameter names from URL routing
        uuid_param = kwargs.get('uuid') or kwargs.get('pk')
        chi_tiet_list = chi_tiet_service.get_by_phieu_nhap(uuid_param)
        serializer = ChiTietPhieuNhapHangBanTraLaiModelSerializer(
            chi_tiet_list, many=True
        )

        return Response(serializer.data, status=status.HTTP_200_OK)
