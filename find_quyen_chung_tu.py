#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'x_erp.settings')
django.setup()

from django_ledger.models import QuyenChungTu

def find_quyen_chung_tu():
    try:
        qct = QuyenChungTu.objects.first()
        if qct:
            print(f'UUID: {qct.uuid}')
            print(f'Ma NK: {qct.ma_nk}')
            print(f'So CT Mau: {qct.so_ct_mau}')
            print(f'Kieu Trung So: {qct.kieu_trung_so}')
            print(f'I So CT HT: {qct.i_so_ct_ht}')
        else:
            print('No QuyenChungTu found')
    except Exception as e:
        print(f'Error: {e}')

if __name__ == "__main__":
    find_quyen_chung_tu()
