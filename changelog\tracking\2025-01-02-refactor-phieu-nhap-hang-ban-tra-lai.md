# Refactor phieu_nhap_hang_ban_tra_lai theo pattern phieu_xuat_kho

## Mục tiêu

Refactor code của `phieu_nhap_hang_ban_tra_lai` theo pattern của `phieu_xuat_kho`, đặc biệt chú ý đến xử lý so_ct và ChungTuMixin.

## Các thay đổi đã thực hiện

### 1. Model chính (phieu_nhap_hang_ban_tra_lai.py)

#### ✅ Cải thiện clean() method

- **Trước**: Clean method tự implement validation cho so_ct và i_so_ct duplicate
- **Sau**: Đơn giản hóa clean method, để ChungTuMixin xử lý validation so_ct/i_so_ct
- **Lý do**: Tránh duplicate logic, follow pattern chuẩn của ChungTuMixin

#### ✅ Cập nhật business validation

- Thêm validation cho số tiền âm
- Thêm validation cho tỷ giá
- Thêm validation cho ngày chứng từ
- Thêm validation cho consistency giữa tiền ngoại tệ và VND

#### ✅ Cải thiện field definitions

- `ma_ngv`: Xóa null=True, blank=True để đồng bộ với phieu_xuat_kho
- `ma_nt`: Thay đổi on_delete từ SET_NULL thành CASCADE, thêm related_name
- `ty_gia`: Thay đổi decimal_places từ 4 thành 2, xóa default=1
- `status`: Thay đổi max_length từ 1 thành 50
- `t_so_luong`, `t_tien`, `t_tien_nt`: Thay đổi decimal_places từ 4 thành 2

#### ✅ Xóa các field không cần thiết

- Xóa các field tổng tiền phức tạp: `t_tien_nt2`, `t_tien2`, `t_ck`, `t_ck_nt`, `t_tc_tien`, `t_tc_tien_nt2`, `t_km`, `t_km_nt`, `t_thue`, `t_thue_nt`, `t_tt`, `t_tt_nt`
- Xóa các flag quản lý: `lo_yn`, `vi_tri_yn`, `qc_yn`

#### ✅ Cập nhật Meta class

- Thêm indexes cho `ma_kh`, `ma_nt`, `unit_id`
- Thêm `ordering = ['-created']`

### 2. Model chi tiết (chi_tiet_phieu_nhap_hang_ban_tra_lai.py)

#### ✅ Cải thiện field definitions

- `ma_vt`: Thay đổi on_delete từ SET_NULL thành CASCADE, xóa null/blank
- `dvt`: Thay đổi on_delete từ SET_NULL thành CASCADE, xóa null/blank
- `ten_dvt`: Thêm default=''

#### ✅ Cập nhật field types

- `gia_nt`, `tien_nt`: Thay đổi từ CharField thành DecimalField
- `ck_nt`, `thue_nt`: Thay đổi từ CharField thành DecimalField
- `thue_suat`: Thay đổi từ CharField thành DecimalField
- Tất cả DecimalField: Thay đổi max_digits thành 18, decimal_places thành 4

#### ✅ Thêm field mới

- `he_so`: DecimalField cho hệ số quy đổi

#### ✅ Xóa field không cần thiết

- Xóa các field giá phức tạp: `gia_nt1`, `gia_nt2`, `tien_nt2`, `gia1`, `gia2`, `tien2`
- Xóa field `ct_km`, `pn_tb`

#### ✅ Cập nhật field quản lý

- `lo_yn`, `vi_tri_yn`, `qc_yn`: Thay đổi từ BooleanField thành IntegerField
- `ma_kho`: Thay đổi on_delete từ SET_NULL thành CASCADE, xóa null/blank
- `ten_kho`, `ten_lo`, `ten_vi_tri`: Thêm default=''

#### ✅ Cải thiện account fields

- Đơn giản hóa account fields, chỉ giữ `tk_vt`, `ma_nx`, `tk_du`
- Thay đổi on_delete thành CASCADE cho các field bắt buộc

#### ✅ Cập nhật document references

- Thay đổi từ CharField thành IntegerField cho các ID field
- Thêm field `sl_px`, `id_yc`, `line_yc`, `id_nhap`, `line_nhap`
- Xóa các field không cần thiết: `so_ct_px`, `id_hd`, `so_ct_hd`, `line_hd`, `id_dh`, `line_dh`

#### ✅ Cập nhật Meta class

- Đơn giản hóa indexes, chỉ giữ các field chính

## Kết quả

### ✅ Hoàn thành

- [x] Refactor model chính theo pattern phieu_xuat_kho
- [x] Refactor model chi tiết theo pattern phieu_xuat_kho
- [x] Đơn giản hóa clean method, dựa vào ChungTuMixin
- [x] Cập nhật field types và constraints
- [x] Xóa các field không cần thiết
- [x] Cập nhật Meta class và indexes
- [x] Kiểm tra syntax errors

### ✅ Cập nhật cuối cùng (sau feedback)

- [x] Xóa clean() method để follow pattern phieu_xuat_kho
- [x] Thêm lại các field bị thiếu từ cURL request
- [x] Cập nhật service \_update_totals method với safe_decimal conversion
- [x] Đảm bảo tất cả field có default values phù hợp
- [x] Kiểm tra serializers, repositories, views hoạt động

### ✅ Sửa lỗi TypeError: argument must be int or float

- [x] Cập nhật service \_update_totals với safe_decimal helper function
- [x] Sửa ma_nt field: on_delete=CASCADE → SET_NULL, thêm blank=True
- [x] Thêm default values cho tất cả CharField fields
- [x] Thêm ma_unit field vào serializer để tránh lỗi
- [x] Thêm ngay_lct field vào serializer
- [x] Sửa các field: dien_giai, ma_so_thue, ten_kh_thue, ong_ba, dia_chi, e_mail, ma_ngv, ma_gd, ma_tthddt, ma_pttt

### 🔄 Tiếp theo cần làm

- [ ] Tạo migration files
- [ ] Test với cURL request thực tế
- [ ] Kiểm tra API endpoint hoạt động

## Lưu ý quan trọng

1. **ChungTuMixin handling**: Model hiện tại đã được refactor để dựa vào ChungTuMixin cho validation so_ct/i_so_ct thay vì tự implement

2. **Field consistency**: Tất cả DecimalField đã được chuẩn hóa với max_digits=18, decimal_places phù hợp

3. **Pattern compliance**: Model đã follow pattern của phieu_xuat_kho về cấu trúc, field types, và validation

4. **Breaking changes**: Một số field đã bị xóa hoặc thay đổi type, cần cập nhật code phụ thuộc
