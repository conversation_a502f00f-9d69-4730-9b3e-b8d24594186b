"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the serializers for the ThueHoaDonMuaHangTrongNuocModel.
"""

from rest_framework import serializers

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (
    ThueHoaDonMuaHangTrongNuocModel,
)


class ThueHoaDonMuaHangTrongNuocModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ThueHoaDonMuaHangTrongNuocModel.
    Used for read operations.
    """

    # Read-only fields for related objects
    hoa_don_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_no_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    so_ct2_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ThueHoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "hoa_don",
            "hoa_don_data",
            "line",
            "so_ct0",
            "so_ct0_data",
            "so_ct2",
            "so_ct2_data",
            "ngay_ct0",
            "ma_thue",
            "ma_thue_data",
            "thue_suat",
            "ma_tc_thue",
            "ma_kh",
            "ma_kh_data",
            "ten_kh_thue",
            "dia_chi",
            "ma_so_thue",
            "ten_vt_thue",
            "t_tien_nt",
            "t_tien",
            "tk_thue_no",
            "tk_thue_no_data",
            "ten_tk_thue_no",
            "t_thue_nt",
            "t_thue",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "hoa_don_data",
            "ma_thue_data",
            "tk_thue_no_data",
            "ma_kh_data",
            "so_ct0_data",
            "so_ct2_data",
            "created",
            "updated",
        ]

    def get_hoa_don_data(self, obj):  # noqa: C901
        """
        Get basic information about the invoice
        """
        if not obj.hoa_don:
            return None

        # Get basic invoice data
        hoa_don_data = {
            "uuid": str(obj.hoa_don.uuid),
            "so_ct": getattr(obj.hoa_don, "so_ct_id", None),
            "ngay_ct": getattr(obj.hoa_don, "ngay_ct", None),
            "ten_kh": getattr(obj.hoa_don, "ten_kh", ""),
        }

        # Add document number if available
        if hasattr(obj.hoa_don, "so_ct") and obj.hoa_don.so_ct:
            hoa_don_data["so_ct_data"] = {
                "uuid": str(obj.hoa_don.so_ct.uuid),
                "ma_ct": getattr(obj.hoa_don.so_ct, "ma_ct", ""),
                "ten_ct": getattr(obj.hoa_don.so_ct, "ten_ct", ""),
            }

        return hoa_don_data

    def get_ma_thue_data(self, obj):  # noqa: C901
        """
        Get basic information about the tax
        """
        if not obj.ma_thue:
            return None

        return {
            "ma_thue": obj.ma_thue,
            "ten_thue": getattr(obj, "ten_thue", ""),
            "thue_suat": getattr(obj, "thue_suat", None),
        }

    def get_tk_thue_no_data(self, obj):  # noqa: C901
        """
        Get basic information about the tax account
        """
        if not obj.tk_thue_no:
            return None

        return {
            "code": obj.tk_thue_no,
            "name": getattr(obj, "ten_tk_thue_no", ""),
        }

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Get basic information about the customer
        """
        if not obj.ma_kh:
            return None

        return {"ma_kh": obj.ma_kh, "ten_kh": getattr(obj, "ten_kh_thue", "")}

    def get_so_ct0_data(self, obj):  # noqa: C901
        """
        Get basic information about the reference document
        """
        if not obj.so_ct0:
            return None

        return {
            "uuid": (str(obj.so_ct0.uuid) if hasattr(obj.so_ct0, "uuid") else None),
            "ma_ct": getattr(obj.so_ct0, "ma_ct", ""),
            "ten_ct": getattr(obj.so_ct0, "ten_ct", ""),
        }

    def get_so_ct2_data(self, obj):  # noqa: C901
        """
        Get basic information about the secondary reference document
        """
        if not obj.so_ct2:
            return None

        return {
            "uuid": (str(obj.so_ct2.uuid) if hasattr(obj.so_ct2, "uuid") else None),
            "ma_ct": getattr(obj.so_ct2, "ma_ct", ""),
            "ten_ct": getattr(obj.so_ct2, "ten_ct", ""),
        }


class ThueHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
    serializers.ModelSerializer
):
    """
    Serializer for the ThueHoaDonMuaHangTrongNuocModel.
    Used for create and update operations.
    """

    class Meta:
        model = ThueHoaDonMuaHangTrongNuocModel
        fields = [
            "line",
            "so_ct0",
            "so_ct2",
            "ngay_ct0",
            "ma_thue",
            "thue_suat",
            "ma_tc_thue",
            "ma_kh",
            "dia_chi",
            "ma_so_thue",
            "ten_vt_thue",
            "t_tien_nt",
            "t_tien",
            "tk_thue_no",
            "t_thue_nt",
            "t_thue",
        ]
        extra_kwargs = {
            'so_ct2': {'required': False},
            'dia_chi': {'required': False},
            'ma_so_thue': {'required': False},
            'ten_vt_thue': {'required': False},
            'tk_thue_no': {'required': False},
        }

    def validate(self, data):  # noqa: C901
        """
        Validate the data before creating or updating the model instance.

        Parameters
        ----------
        data : dict
            The data to validate.

        Returns
        -------
        dict
            The validated data.
        """
        # Auto-calculate tax amount if not provided
        if "t_thue_nt" not in data and "t_tien_nt" in data and "thue_suat" in data:
            data["t_thue_nt"] = data["t_tien_nt"] * (data["thue_suat"] / 100)
        # Auto-calculate tax amount in local currency if not provided
        if "t_thue" not in data and "t_tien" in data and "thue_suat" in data:
            data["t_thue"] = data["t_tien"] * (data["thue_suat"] / 100)

        return data
