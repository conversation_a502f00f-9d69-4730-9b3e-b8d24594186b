"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ChiTietPhieuNhapHangBanTraLai (Customer Return Receipt Detail) model.
"""

from django.utils.translation import gettext_lazy as _  # noqa: F401
from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.account import AccountModelSerializer  # noqa: F401
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (  # noqa: F401,
    KheUocModelSerializer,
)
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer  # noqa: F401,
from django_ledger.api.serializers.lo import LoModelSerializer  # noqa: F401,
from django_ledger.api.serializers.organization import (  # noqa: F401,
    BoPhanModelSerializer,
)
from django_ledger.api.serializers.vat_tu import VatTuSerializer  # noqa: F401,
from django_ledger.api.serializers.vi_tri import ViTriModelSerializer  # noqa: F401,
from django_ledger.api.serializers.warehouse import (  # noqa: F401,
    KhoHangModelSerializer,
)
from django_ledger.api.serializers.contract import ContractModelSerializer  # noqa: F401,
from django_ledger.api.serializers.tax import TaxModelSerializer  # noqa: F401,
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer  # noqa: F401,
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer  # noqa: F401,
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    ChiTietPhieuNhapHangBanTraLaiModel,
)


class ChiTietPhieuNhapHangBanTraLaiModelSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhieuNhapHangBanTraLai model.
    """

    # Read-only fields for related objects
    phieu_nhap_hang_ban_tra_lai_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    ma_lo_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_no_data = serializers.SerializerMethodField(read_only=True)
    tk_dt_data = serializers.SerializerMethodField(read_only=True)
    tk_gv_data = serializers.SerializerMethodField(read_only=True)
    tk_vt_data = serializers.SerializerMethodField(read_only=True)
    tk_ck_data = serializers.SerializerMethodField(read_only=True)
    tk_km_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietPhieuNhapHangBanTraLaiModel
        fields = [
            'uuid',
            'phieu_nhap_hang_ban_tra_lai',
            'phieu_nhap_hang_ban_tra_lai_data',
            'line',
            # Product info
            'ma_vt',
            'ma_vt_data',

            'dvt',
            'dvt_data',
            'ten_dvt',
            # Warehouse info
            'ma_kho',
            'ma_kho_data',
            'ma_lo',
            'ma_lo_data',
            'ma_vi_tri',
            'ma_vi_tri_data',
            # Account info
            'tk_thue_no',
            'tk_thue_no_data',
            'tk_dt',
            'tk_dt_data',
            'tk_gv',
            'tk_gv_data',
            'tk_vt',
            'tk_vt_data',
            'tk_ck',
            'tk_ck_data',
            'tk_km',
            'tk_km_data',
            # Business dimensions
            'ma_bp',
            'ma_bp_data',
            # Quantity and pricing
            'so_luong',
            'ct_km',
            'pn_tb',
            'gia_nt',
            'tien_nt',
            'gia_nt1',
            'gia_nt2',
            'tien_nt2',
            'tl_ck',
            'ck_nt',
            'don_gia',
            'tien',
            # Tax info
            'ma_thue',
            'ma_thue_data',
            'tk_thue_no',
            'tk_thue_no_data',
            'thue_suat',
            'thue_nt',
            'thue',
            # Account info
            'tk_dt',
            'tk_dt_data',
            'tk_gv',
            'tk_gv_data',
            'tk_vt',
            'tk_vt_data',
            'tk_ck',
            'tk_ck_data',
            'tk_km',
            'tk_km_data',
            # Business dimensions
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            # Document references
            'so_ct_hd',
            'line_hd',
            # Timestamps
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'phieu_nhap_hang_ban_tra_lai',
            'phieu_nhap_hang_ban_tra_lai_data',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'ma_vi_tri_data',
            'ma_lo_data',
            'tk_thue_no_data',
            'tk_dt_data',
            'tk_gv_data',
            'tk_vt_data',
            'tk_ck_data',
            'tk_km_data',
            'ma_bp_data',
            'ma_ku_data',
            'ma_thue_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_sp_data',
            'created',
            'updated',
        ]

    def get_phieu_nhap_hang_ban_tra_lai_data(self, obj):  # noqa: C901
        """
        Get phieu_nhap_hang_ban_tra_lai data.
        """
        if obj.phieu_nhap_hang_ban_tra_lai:
            return {
                'uuid': obj.phieu_nhap_hang_ban_tra_lai.uuid,
                'so_ct': obj.phieu_nhap_hang_ban_tra_lai.so_ct,
                'ngay_ct': obj.phieu_nhap_hang_ban_tra_lai.ngay_ct,
            }
        return None

    def get_ma_vt_data(self, obj):  # noqa: C901
        """
        Get ma_vt data.
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        """
        Get dvt data.
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):  # noqa: C901
        """
        Get ma_kho data.
        """
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_vi_tri_data(self, obj):  # noqa: C901
        """
        Get ma_vi_tri data.
        """
        if obj.ma_vi_tri:
            return ViTriModelSerializer(obj.ma_vi_tri).data
        return None

    def get_ma_lo_data(self, obj):  # noqa: C901
        """
        Get ma_lo data.
        """
        if obj.ma_lo:
            return LoModelSerializer(obj.ma_lo).data
        return None

    def get_tk_thue_no_data(self, obj):  # noqa: C901
        """
        Get tk_thue_no data.
        """
        if obj.tk_thue_no:
            return AccountModelSerializer(obj.tk_thue_no).data
        return None

    def get_tk_dt_data(self, obj):  # noqa: C901
        """
        Get tk_dt data.
        """
        if obj.tk_dt:
            return AccountModelSerializer(obj.tk_dt).data
        return None

    def get_tk_gv_data(self, obj):  # noqa: C901
        """
        Get tk_gv data.
        """
        if obj.tk_gv:
            return AccountModelSerializer(obj.tk_gv).data
        return None

    def get_tk_vt_data(self, obj):  # noqa: C901
        """
        Get tk_vt data.
        """
        if obj.tk_vt:
            return AccountModelSerializer(obj.tk_vt).data
        return None

    def get_tk_ck_data(self, obj):  # noqa: C901
        """
        Get tk_ck data.
        """
        if obj.tk_ck:
            return AccountModelSerializer(obj.tk_ck).data
        return None

    def get_tk_km_data(self, obj):  # noqa: C901
        """
        Get tk_km data.
        """
        if obj.tk_km:
            return AccountModelSerializer(obj.tk_km).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Get ma_bp data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """
        Get ma_ku data.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_thue_data(self, obj):  # noqa: C901
        """
        Get ma_thue data.
        """
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """
        Get ma_vv data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """
        Get ma_hd data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """
        Get ma_dtt data.
        """
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """
        Get ma_sp data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None


class ChiTietPhieuNhapHangBanTraLaiModelCreateUpdateSerializer(
    serializers.ModelSerializer
):
    """
    Serializer for creating and updating ChiTietPhieuNhapHangBanTraLai model.
    """

    class Meta:
        model = ChiTietPhieuNhapHangBanTraLaiModel
        fields = [
            'phieu_nhap_hang_ban_tra_lai',
            'line',
            # Product info
            'ma_vt',
            'ten_vt',
            'dvt',
            'ten_dvt',
            # Warehouse info
            'ma_kho',
            'ten_kho',
            'ma_lo',
            'ten_lo',
            'ma_vi_tri',
            'ten_vi_tri',
            # Quantity and pricing
            'so_luong',
            'ct_km',
            'pn_tb',
            'gia_nt',
            'tien_nt',
            'gia_nt1',
            'gia_nt2',
            'tien_nt2',
            'tl_ck',
            'ck_nt',
            'don_gia',
            'tien',
            # Tax info
            'ma_thue',
            'tk_thue_no',
            'thue_suat',
            'thue_nt',
            'thue',
            # Account info
            'tk_dt',
            'tk_gv',
            'tk_vt',
            'tk_ck',
            'tk_km',
            # Business dimensions
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
            # Document references
            'so_ct_hd',
            'line_hd',
        ]
        read_only_fields = ['uuid']

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # List of ForeignKey fields that can be null
        nullable_fk_fields = [
            'ma_vt',
            'dvt',
            'ma_kho',
            'ma_lo',
            'ma_vi_tri',
            'ma_thue',
            'tk_thue_no',
            'tk_dt',
            'tk_gv',
            'tk_vt',
            'tk_ck',
            'tk_km',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_cp0',
        ]

        # Convert empty strings to None for nullable ForeignKey fields
        for field in nullable_fk_fields:
            if field in attrs and attrs[field] == "":
                attrs[field] = None

        # List of CharField fields that can be null
        nullable_char_fields = [
            'ma_lsx',
            'so_ct_hd',
            'id_hd',
            'id_dh',
            'id_px',
        ]

        # Convert empty strings to None for nullable CharField fields
        for field in nullable_char_fields:
            if field in attrs and (attrs[field] == "" or attrs[field] is None):
                attrs[field] = None

        # Handle nullable integer fields
        nullable_int_fields = [
            'line_hd',
            'line_dh',
            'line_px',
        ]
        for field in nullable_int_fields:
            if field in attrs and attrs[field] is None:
                attrs[field] = None

        # Validate decimal fields are non-negative
        decimal_fields = [
            'so_luong',
            'don_gia',
            'tien',
            'thue',
        ]
        for field in decimal_fields:
            if field in attrs and attrs[field] is not None and attrs[field] < 0:
                raise serializers.ValidationError(
                    {field: _('This field must be non-negative.')}
                )

        return attrs

    def to_representation(self, instance):
        """
        Override to handle decimal field conversion errors
        """
        from decimal import Decimal, InvalidOperation
        import logging

        logger = logging.getLogger(__name__)

        try:
            # Try to access each decimal field individually and fix invalid values
            decimal_fields = [
                'so_luong', 'he_so', 'gia_nt', 'tien_nt', 'gia_nt1', 'gia_nt2', 'tien_nt2',
                'don_gia', 'tien', 'gia', 'gia1', 'gia2', 'tien2', 'tl_ck', 'ck_nt', 'ck',
                'thue_suat', 'thue_nt', 'thue'
            ]

            # Check and fix decimal fields before serialization
            for field_name in decimal_fields:
                if hasattr(instance, field_name):
                    try:
                        value = getattr(instance, field_name)
                        if value is not None:
                            # Try to convert to Decimal to validate
                            if isinstance(value, str):
                                # If it's a string, try to convert to Decimal
                                try:
                                    decimal_value = Decimal(value)
                                    setattr(instance, field_name, decimal_value)
                                except (InvalidOperation, ValueError):
                                    logger.warning(f"Invalid decimal value '{value}' for field {field_name}, setting to 0")
                                    setattr(instance, field_name, Decimal('0'))
                            elif not isinstance(value, Decimal):
                                # If it's not a Decimal, try to convert
                                try:
                                    decimal_value = Decimal(str(value))
                                    setattr(instance, field_name, decimal_value)
                                except (InvalidOperation, ValueError):
                                    logger.warning(f"Cannot convert '{value}' to decimal for field {field_name}, setting to 0")
                                    setattr(instance, field_name, Decimal('0'))
                    except Exception as field_error:
                        logger.error(f"Error processing field {field_name}: {field_error}")
                        setattr(instance, field_name, Decimal('0'))

            return super().to_representation(instance)

        except Exception as e:
            logger.error(f"Error serializing chi_tiet instance {instance.uuid}: {e}")

            # Return a minimal safe representation
            return {
                'uuid': str(instance.uuid),
                'line': getattr(instance, 'line', 1),
                'so_luong': '0.00',
                'don_gia': '0.00',
                'tien': '0.00',
                'error': 'Serialization error - using default values'
            }


class ChiTietPhieuNhapHangBanTraLaiNestedSerializer(serializers.ModelSerializer):
    """
    Nested serializer for ChiTietPhieuNhapHangBanTraLai model used in PhieuNhapHangBanTraLai.
    """

    class Meta:
        model = ChiTietPhieuNhapHangBanTraLaiModel
        fields = [
            'line',
            # Product info
            'ma_vt',
            'ten_vt',
            'dvt',
            'ten_dvt',
            # Warehouse info
            'ma_kho',
            'ten_kho',
            'ma_lo',
            'ten_lo',
            'ma_vi_tri',
            'ten_vi_tri',
            # Quantity and pricing
            'so_luong',
            'ct_km',
            'pn_tb',
            'gia_nt',
            'tien_nt',
            'gia_nt1',
            'gia_nt2',
            'tien_nt2',
            'tl_ck',
            'ck_nt',
            # Tax info
            'ma_thue',
            'tk_thue_no',
            'thue_suat',
            'thue_nt',
            # Account info
            'tk_dt',
            'tk_gv',
            'tk_vt',
            'tk_ck',
            'tk_km',
            # Business dimensions
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
            # Document references
            'so_ct_hd',
            'line_hd',
        ]
