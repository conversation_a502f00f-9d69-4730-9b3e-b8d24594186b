"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for ChiTietPhieuNhapHangBanTraLai (Customer Return Receipt Detail) model.
"""

from typing import Any, Dict, List, Optional  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401

# Import the required models
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    ChiTietPhieuNhapHangBanTraLaiModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class ChiTietPhieuNhapHangBanTraLaiRepository(BaseRepository):
    """
    Repository class for handling ChiTietPhieuNhapHangBanTraLai model database operations.  # noqa: E501
    Implements the Repository pattern for ChiTietPhieuNhapHangBanTraLai.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the ChiTietPhieuNhapHangBanTraLaiModel.
        """
        super().__init__(model_class=ChiTietPhieuNhapHangBanTraLaiModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for ChiTietPhieuNhapHangBanTraLaiModel.

        Returns
        -------
        QuerySet
            The base queryset for ChiTietPhieuNhapHangBanTraLaiModel.
        """
        return self.model_class.objects.all().select_related(
            'phieu_nhap_hang_ban_tra_lai',
            'ma_vt',
            'dvt',
            'ma_kho',
            'ma_vi_tri',
            'ma_lo',
            'ma_thue',
            'tk_thue_no',
            'tk_dt',
            'tk_gv',
            'tk_vt',
            'tk_ck',
            'tk_km',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_sp',
        )

    def get_by_uuid(
        self, uuid: UUID
    ) -> Optional[ChiTietPhieuNhapHangBanTraLaiModel]:  # noqa: C901
        """
        Get a ChiTietPhieuNhapHangBanTraLaiModel instance by UUID.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiTietPhieuNhapHangBanTraLaiModel

        Returns
        -------
        Optional[ChiTietPhieuNhapHangBanTraLaiModel]
            The ChiTietPhieuNhapHangBanTraLaiModel instance if found, None otherwise
        """
        try:
            return self.model_class.objects.get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def get_by_phieu_nhap(
        self, phieu_nhap_uuid: UUID
    ) -> QuerySet:  # noqa: C901
        """
        Get a queryset of ChiTietPhieuNhapHangBanTraLaiModel instances for a specific PhieuNhapHangBanTraLaiModel.  # noqa: E501

        Parameters
        ----------
        phieu_nhap_uuid : UUID
            The UUID of the PhieuNhapHangBanTraLaiModel

        Returns
        -------
        QuerySet
            A queryset of ChiTietPhieuNhapHangBanTraLaiModel instances
        """
        return self.model_class.objects.filter(
            phieu_nhap_hang_ban_tra_lai_id=phieu_nhap_uuid
        ).order_by('line')



    def create(
        self, data: Dict[str, Any]
    ) -> ChiTietPhieuNhapHangBanTraLaiModel:  # noqa: C901
        """
        Create a new customer return receipt detail

        Parameters
        ----------
        data : Dict[str, Any]
            The data to create the customer return receipt detail with

        Returns
        -------
        ChiTietPhieuNhapHangBanTraLaiModel
            The created customer return receipt detail
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            # Convert UUIDs to model instances
            data = self.convert_uuids_to_model_instances(data)

            # Log the data for debugging
            logger.debug(f"Creating chi_tiet with data: {data}")

            # Create customer return receipt detail
            return self.model_class.objects.create(**data)
        except Exception as e:
            logger.error(f"Error creating chi_tiet: {e}")
            logger.error(f"Data: {data}")
            raise



    def update(
        self, uuid: UUID, data: Dict[str, Any]
    ) -> ChiTietPhieuNhapHangBanTraLaiModel:  # noqa: C901
        """
        Update a customer return receipt detail

        Parameters
        ----------
        uuid : UUID
            The UUID of the customer return receipt detail
        data : Dict[str, Any]
            The data to update the customer return receipt detail with

        Returns
        -------
        ChiTietPhieuNhapHangBanTraLaiModel
            The updated customer return receipt detail
        """
        # Convert UUIDs to model instances using BaseRepository method
        data = self.convert_uuids_to_model_instances(data)
        # Get the instance
        instance = self.get_by_uuid(uuid)
        if not instance:
            raise self.model_class.DoesNotExist(
                f"ChiTietPhieuNhapHangBanTraLaiModel with UUID {uuid} does not exist"
            )

        # Update fields
        for key, value in data.items():
            setattr(instance, key, value)

        # Save the instance
        instance.save()

        return instance

    def delete(self, uuid: UUID) -> bool:  # noqa: C901
        """
        Delete a customer return receipt detail

        Parameters
        ----------
        uuid : UUID
            The UUID of the customer return receipt detail

        Returns
        -------
        bool
            True if the customer return receipt detail was deleted, False otherwise
        """
        instance = self.get_by_uuid(uuid)
        if not instance:
            return False

        instance.delete()
        return True

    def create_batch(
        self, phieu_nhap_uuid: UUID, details: List[Dict[str, Any]]
    ) -> List[ChiTietPhieuNhapHangBanTraLaiModel]:  # noqa: C901
        """
        Create multiple customer return receipt details

        Parameters
        ----------
        phieu_nhap_uuid : UUID
            The UUID of the PhieuNhapHangBanTraLaiModel
        details : List[Dict[str, Any]]
            The list of details to create

        Returns
        -------
        List[ChiTietPhieuNhapHangBanTraLaiModel]
            The list of created customer return receipt details
        """
        created_details = []
        for detail in details:
            # Add phieu_nhap_uuid to detail
            detail['phieu_nhap_hang_ban_tra_lai'] = phieu_nhap_uuid
            # Create detail
            created_detail = self.create(detail)
            created_details.append(created_detail)
        return created_details
