"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapHangBanTraLai model, which represents a Customer Return Receipt  # noqa: E501
which the EntityModel issues when receiving returned goods from customers.
"""

from uuid import uuid4  # noqa: F401

from django.core.exceptions import ValidationError  # noqa: F401
from django.db import models  # noqa: F401
from django.utils import timezone  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,
from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,


class PhieuNhapHangBanTraLaiModelQueryset(models.QuerySet):
    """
    A custom defined QuerySet for the PhieuNhapHangBanTraLaiModel. This implements multiple methods or queries needed to get a  # noqa: E501
    filtered QuerySet based on the PhieuNhapHangBanTraLaiModel status.
    """

    def active(self) -> models.QuerySet:  # noqa: C901
        """
        Active customer return receipts can be viewed and processed.

        Returns
        -------
        PhieuNhapHangBanTraLaiModelQueryset
            A QuerySet of active Customer Return Receipts.
        """
        return self.filter(status="1")

    def inactive(self) -> models.QuerySet:  # noqa: C901
        """
        Inactive customer return receipts are hidden from view.

        Returns
        -------
        PhieuNhapHangBanTraLaiModelQueryset
            A QuerySet of inactive Customer Return Receipts.
        """
        return self.exclude(status="1")


class PhieuNhapHangBanTraLaiModelManager(models.Manager):
    """
    A custom defined PhieuNhapHangBanTraLaiModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    PhieuNhapHangBanTraLaiModel.
    """

    def for_entity(self, entity_slug, user_model):  # noqa: C901
        """
        Fetches a QuerySet of PhieuNhapHangBanTraLaiModels that the UserModel has access to.  # noqa: E501
        May include PhieuNhapHangBanTraLaiModels from multiple Entities.

        Parameters
        __________
        entity_slug: str
            The entity slug to filter by.
        user_model
            Logged in and authenticated django UserModel instance.
        """
        qs = self.get_queryset()
        return qs.filter(entity_model__slug__exact=entity_slug)


class PhieuNhapHangBanTraLaiModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the PhieuNhapHangBanTraLaiModel database will inherit from.  # noqa: E501
    The PhieuNhapHangBanTraLaiModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    entity_model: EntityModel
        The EntityModel associated with this customer return receipt.

    ma_ngv: str
        The delivery person code.

    ma_kh: str
        The customer code.

    ma_so_thue: str
        The tax code.

    ten_kh_thue: str
        The customer tax name.

    ong_ba: str
        The contact person.

    dia_chi: str
        The address.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity Model'),
    )

    # Thông tin chứng từ bổ sung (ma_nk, so_ct, ngay_ct, i_so_ct now handled by ChungTuMixIn)
    dien_giai = models.CharField(
        max_length=255,
        verbose_name=_('Diễn giải'),
        help_text=_('Diễn giải'),
    )

    # Thông tin khách hàng
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã KH'),
        help_text=_('Mã khách hàng'),
    )
    ma_so_thue = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Mã số thuế'),
        help_text=_('Mã số thuế'),
    )
    ten_kh_thue = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_('Tên KH thuế'),
        help_text=_('Tên khách hàng thuế'),
    )
    ong_ba = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_('Ông/Bà'),
        help_text=_('Người liên hệ'),
    )
    dia_chi = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_('Địa chỉ'),
        help_text=_('Địa chỉ'),
    )
    e_mail = models.EmailField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_('Email'),
        help_text=_('Email'),
    )

    # Thông tin giao nhận
    ma_ngv = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('Mã NGV'),
        help_text=_('Mã người giao vận'),
    )
    ma_gd = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('Mã giao dịch'),
        help_text=_('Mã giao dịch'),
    )
    ma_nv = models.ForeignKey(
        'django_ledger.NhanVienModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã NV'),
        help_text=_('Mã nhân viên'),
    )

    # Thông tin tài khoản và đơn vị
    tk = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Tài khoản'),
        help_text=_('Tài khoản'),
    )
    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.CASCADE,
        verbose_name=_('Unit'),
        related_name='phieu_nhap_hang_ban_tra_lai',
        null=True,
        blank=True,
    )

    # Thông tin chứng từ bổ sung
    so_ct0 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Số CT 0'),
        help_text=_('Số chứng từ 0'),
    )
    so_ct2 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Số CT 2'),
        help_text=_('Số chứng từ 2'),
    )
    ngay_ct0 = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Ngày CT 0'),
        help_text=_('Ngày chứng từ 0'),
    )


    # Thông tin thanh toán
    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã NT'),
        related_name='phieu_nhap_hang_ban_tra_lai',
        null=True,
        help_text=_('Mã ngoại tệ'),
    )
    ty_gia = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=1,
        verbose_name=_('Tỷ giá'),
        help_text=_('Tỷ giá'),
    )
    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã thuế'),
        help_text=_('Mã thuế'),
    )

    # Trạng thái và cờ
    status = models.CharField(
        max_length=50,
        default='1',
        verbose_name=_('Trạng thái'),
        help_text=_('Trạng thái'),
    )
    transfer_yn = models.BooleanField(
        default=False,
        verbose_name=_('Chuyển khoản'),
        help_text=_('Có chuyển khoản'),
    )

    # Thông tin hóa đơn điện tử
    ma_tthddt = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_('Mã TTHDDT'),
        help_text=_('Mã trạng thái hóa đơn điện tử'),
    )
    ma_pttt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Mã PTTT'),
        help_text=_('Mã phương thức thanh toán'),
    )
    so_ct_hddt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Số CT HDDT'),
        help_text=_('Số chứng từ hóa đơn điện tử'),
    )
    ngay_ct_hddt = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Ngày CT HDDT'),
        help_text=_('Ngày chứng từ hóa đơn điện tử'),
    )
    so_ct2_hddt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Số CT2 HDDT'),
        help_text=_('Số chứng từ 2 hóa đơn điện tử'),
    )
    ma_mau_ct_hddt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Mã mẫu CT HDDT'),
        help_text=_('Mã mẫu chứng từ hóa đơn điện tử'),
    )
    invat_yn = models.BooleanField(
        default=False,
        verbose_name=_('Có VAT'),
        help_text=_('Có thuế VAT'),
    )

    # Thông tin bổ sung
    ma_mau_bc = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_('Mã mẫu BC'),
        help_text=_('Mã mẫu báo cáo'),
    )
    ma_tc_thue = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_('Mã TC thuế'),
        help_text=_('Mã tính chất thuế'),
    )
    ma_gd = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_('Mã GD'),
        help_text=_('Mã giao dịch'),
    )
    ghi_chu = models.TextField(
        null=True,
        blank=True,
        verbose_name=_('Ghi chú'),
        help_text=_('Ghi chú'),
    )
    ly_do = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_('Lý do'),
        help_text=_('Lý do'),
    )

    # Tổng số lượng và tiền
    t_so_luong = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng số lượng'),
        help_text=_('Tổng số lượng'),
    )
    t_tien_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng tiền ngoại tệ'),
        help_text=_('Tổng tiền ngoại tệ'),
    )
    t_tien = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng tiền'),
        help_text=_('Tổng tiền'),
    )

    # Các field tổng tiền khác từ API request
    t_tien_nt2 = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng tiền NT2'),
        help_text=_('Tổng tiền ngoại tệ 2'),
    )
    t_tien2 = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng tiền 2'),
        help_text=_('Tổng tiền 2'),
    )
    t_ck = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng chiết khấu'),
        help_text=_('Tổng chiết khấu'),
    )
    t_ck_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng CK NT'),
        help_text=_('Tổng chiết khấu ngoại tệ'),
    )
    t_tc_tien = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng tiền tính chất'),
        help_text=_('Tổng tiền tính chất'),
    )
    t_tc_tien_nt2 = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng tiền TC NT2'),
        help_text=_('Tổng tiền tính chất ngoại tệ 2'),
    )
    t_km = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng khuyến mãi'),
        help_text=_('Tổng khuyến mãi'),
    )
    t_km_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng KM NT'),
        help_text=_('Tổng khuyến mãi ngoại tệ'),
    )
    t_thue = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng thuế'),
        help_text=_('Tổng thuế'),
    )
    t_thue_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng thuế NT'),
        help_text=_('Tổng thuế ngoại tệ'),
    )
    t_tt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng thanh toán'),
        help_text=_('Tổng thanh toán'),
    )
    t_tt_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Tổng TT NT'),
        help_text=_('Tổng thanh toán ngoại tệ'),
    )

    # Cờ quản lý
    lo_yn = models.BooleanField(
        default=False,
        verbose_name=_('Có lô'),
        help_text=_('Có quản lý theo lô'),
    )
    vi_tri_yn = models.BooleanField(
        default=False,
        verbose_name=_('Có vị trí'),
        help_text=_('Có quản lý theo vị trí'),
    )
    qc_yn = models.BooleanField(
        default=False,
        verbose_name=_('Có QC'),
        help_text=_('Có quản lý chất lượng'),
    )




    # Thông tin nhân viên và khách hàng bổ sung
    ma_nvbh = models.ForeignKey(
        'django_ledger.NhanVienModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='phieu_nhap_hang_ban_tra_lai_nvbh',
        verbose_name=_('Mã NVBH'),
        help_text=_('Mã nhân viên bán hàng'),
    )
    ma_mau_ct = models.ForeignKey(
        'django_ledger.MauSoHDModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã mẫu CT'),
        help_text=_('Mã mẫu chứng từ'),
    )
    ma_tt = models.ForeignKey(
        'django_ledger.HanThanhToanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã TT'),
        help_text=_('Mã hạn thanh toán'),
    )
    # i_so_ct now handled by ChungTuMixIn
    ma_dc = models.ForeignKey(
        'django_ledger.DiaChiModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã DC'),
        help_text=_('Mã địa chỉ'),
    )
    ma_ptvc = models.ForeignKey(
        'django_ledger.PhuongTienVanChuyenModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã PTVC'),
        help_text=_('Mã phương tiện vận chuyển'),
    )
    ma_ptgh = models.ForeignKey(
        'django_ledger.PhuongTienGiaoHangModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã PTGH'),
        help_text=_('Mã phương tiện giao hàng'),
    )
    ma_kh9 = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='phieu_nhap_hang_ban_tra_lai_kh9',
        verbose_name=_('Mã KH9'),
        help_text=_('Mã khách hàng phụ'),
    )

    objects = PhieuNhapHangBanTraLaiModelManager.from_queryset(
        PhieuNhapHangBanTraLaiModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Phiếu Nhập Hàng Bán Trả Lại')
        verbose_name_plural = _('Phiếu Nhập Hàng Bán Trả Lại')
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['ma_nt']),
            models.Index(fields=['unit_id']),
            models.Index(fields=['status']),
            # ma_nk, so_ct, ngay_ct indexes are handled by ChungTuMixIn
        ]
        ordering = ['-created']

    def __str__(self):  # noqa: C901
        return f'{self.so_ct}: {self.dien_giai}'


class PhieuNhapHangBanTraLaiModel(PhieuNhapHangBanTraLaiModelAbstract):
    """
    Base Customer Return Receipt Model Implementation
    """

    class Meta(PhieuNhapHangBanTraLaiModelAbstract.Meta):
        abstract = False
        db_table = 'phieu_nhap_hang_ban_tra_lai'
