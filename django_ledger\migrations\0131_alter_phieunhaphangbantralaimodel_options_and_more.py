# Generated by Django 4.2.10 on 2025-07-03 01:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('django_ledger', '0130_hoadondichvumodel_ledger_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='phieunhaphangbantralaimodel',
            options={'ordering': ['-created'], 'verbose_name': '<PERSON>ếu <PERSON>p <PERSON>àng Bán Trả Lại', 'verbose_name_plural': '<PERSON>ế<PERSON> Hàng Bán Trả Lại'},
        ),
        migrations.AddField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ten_kho',
            field=models.CharField(default='', help_text='Tên kho', max_length=100, verbose_name='Tên kho'),
        ),
        migrations.AddField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ten_lo',
            field=models.CharField(default='', help_text='Tên lô', max_length=100, verbose_name='Tên lô'),
        ),
        migrations.AddField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ten_vi_tri',
            field=models.CharField(default='', help_text='Tên vị trí', max_length=100, verbose_name='Tên vị trí'),
        ),
        migrations.AddField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ten_vt',
            field=models.CharField(default='', help_text='Tên vật tư', max_length=255, verbose_name='Tên VT'),
        ),
        migrations.AddField(
            model_name='phieunhaphangbantralaimodel',
            name='t_ck',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng chiết khấu', max_digits=18, verbose_name='Tổng chiết khấu'),
        ),
        migrations.AddField(
            model_name='phieunhaphangbantralaimodel',
            name='t_ck_nt',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng chiết khấu ngoại tệ', max_digits=18, verbose_name='Tổng CK NT'),
        ),
        migrations.AddField(
            model_name='phieunhaphangbantralaimodel',
            name='t_km',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng khuyến mãi', max_digits=18, verbose_name='Tổng khuyến mãi'),
        ),
        migrations.AddField(
            model_name='phieunhaphangbantralaimodel',
            name='t_km_nt',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng khuyến mãi ngoại tệ', max_digits=18, verbose_name='Tổng KM NT'),
        ),
        migrations.AddField(
            model_name='phieunhaphangbantralaimodel',
            name='t_tc_tien',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng tiền tính chất', max_digits=18, verbose_name='Tổng tiền tính chất'),
        ),
        migrations.AddField(
            model_name='phieunhaphangbantralaimodel',
            name='t_tc_tien_nt2',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng tiền tính chất ngoại tệ 2', max_digits=18, verbose_name='Tổng tiền TC NT2'),
        ),
        migrations.AddField(
            model_name='phieunhaphangbantralaimodel',
            name='t_tien_nt',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng tiền ngoại tệ', max_digits=18, verbose_name='Tổng tiền ngoại tệ'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ck',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Chiết khấu', max_digits=18, verbose_name='Chiết khấu'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ck_nt',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Chiết khấu ngoại tệ', max_digits=18, verbose_name='Chiết khấu ngoại tệ'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ct_km',
            field=models.CharField(default='0', help_text='Chiết khấu khuyến mãi', max_length=50, verbose_name='Chiết khấu khuyến mãi'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='don_gia',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Đơn giá', max_digits=18, verbose_name='Đơn giá'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='gia',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Giá', max_digits=18, verbose_name='Giá'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='gia1',
            field=models.DecimalField(blank=True, decimal_places=4, help_text='Giá 1', max_digits=18, null=True, verbose_name='Giá 1'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='gia2',
            field=models.DecimalField(blank=True, decimal_places=4, help_text='Giá 2', max_digits=18, null=True, verbose_name='Giá 2'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='gia_nt',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Giá ngoại tệ', max_digits=18, verbose_name='Giá ngoại tệ'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='gia_nt1',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Giá ngoại tệ 1', max_digits=18, verbose_name='Giá ngoại tệ 1'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='gia_nt2',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Giá ngoại tệ 2', max_digits=18, verbose_name='Giá ngoại tệ 2'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='he_so',
            field=models.DecimalField(decimal_places=6, default=1, help_text='Hệ số quy đổi', max_digits=18, verbose_name='Hệ số'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ma_lo',
            field=models.ForeignKey(help_text='Mã lô', null=True, on_delete=django.db.models.deletion.SET_NULL, to='django_ledger.lomodel', verbose_name='Mã lô'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ma_lsx',
            field=models.CharField(default='', help_text='Mã lệnh sản xuất', max_length=50, verbose_name='Mã lệnh sản xuất'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ma_vi_tri',
            field=models.ForeignKey(help_text='Mã vị trí', null=True, on_delete=django.db.models.deletion.SET_NULL, to='django_ledger.vitrimodel', verbose_name='Mã vị trí'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='ten_dvt',
            field=models.CharField(default='', help_text='Tên đơn vị tính', max_length=100, verbose_name='Tên ĐVT'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='thue',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Thuế', max_digits=18, verbose_name='Thuế'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='thue_nt',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Thuế ngoại tệ', max_digits=18, verbose_name='Thuế ngoại tệ'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='thue_suat',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Thuế suất', max_digits=5, verbose_name='Thuế suất'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='tien',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Tiền', max_digits=18, verbose_name='Tiền'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='tien2',
            field=models.DecimalField(blank=True, decimal_places=4, help_text='Tiền 2', max_digits=18, null=True, verbose_name='Tiền 2'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='tien_nt',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Tiền ngoại tệ', max_digits=18, verbose_name='Tiền ngoại tệ'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='tien_nt2',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Tiền ngoại tệ 2', max_digits=18, verbose_name='Tiền ngoại tệ 2'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='tl_ck',
            field=models.DecimalField(decimal_places=4, default=0, help_text='Tỷ lệ chiết khấu', max_digits=18, verbose_name='Tỷ lệ CK'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='dia_chi',
            field=models.CharField(default='', help_text='Địa chỉ', max_length=255, verbose_name='Địa chỉ'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='dien_giai',
            field=models.CharField(default='', help_text='Diễn giải', max_length=255, verbose_name='Diễn giải'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='e_mail',
            field=models.EmailField(default='', help_text='Email', max_length=255, verbose_name='Email'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='ma_mau_bc',
            field=models.CharField(default='', help_text='Mã mẫu báo cáo', max_length=10, verbose_name='Mã mẫu BC'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='ma_ngv',
            field=models.CharField(default='', help_text='Mã người giao vận', max_length=50, verbose_name='Mã NGV'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='ma_nt',
            field=models.ForeignKey(blank=True, help_text='Mã ngoại tệ', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='phieu_nhap_hang_ban_tra_lai', to='django_ledger.ngoaitemodel', verbose_name='Mã NT'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='ma_pttt',
            field=models.ForeignKey(blank=True, help_text='Mã phương thức thanh toán', null=True, on_delete=django.db.models.deletion.SET_NULL, to='django_ledger.phuongthucthanhtoanmodel', verbose_name='Mã PTTT'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='ma_so_thue',
            field=models.CharField(default='', help_text='Mã số thuế', max_length=50, verbose_name='Mã số thuế'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='ma_tc_thue',
            field=models.CharField(default='', help_text='Mã tính chất thuế', max_length=10, verbose_name='Mã TC thuế'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='ma_tthddt',
            field=models.CharField(default='0', help_text='Mã trạng thái hóa đơn điện tử', max_length=10, verbose_name='Mã TTHDDT'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='ong_ba',
            field=models.CharField(default='', help_text='Người liên hệ', max_length=100, verbose_name='Ông/Bà'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='status',
            field=models.CharField(default='1', help_text='Trạng thái', max_length=50, verbose_name='Trạng thái'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='t_so_luong',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng số lượng', max_digits=18, verbose_name='Tổng số lượng'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='t_thue',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng thuế', max_digits=18, verbose_name='Tổng thuế'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='t_thue_nt',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng thuế ngoại tệ', max_digits=18, verbose_name='Tổng thuế NT'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='t_tien',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng tiền', max_digits=18, verbose_name='Tổng tiền'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='t_tien2',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng tiền 2', max_digits=18, verbose_name='Tổng tiền 2'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='t_tien_nt2',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng tiền ngoại tệ 2', max_digits=18, verbose_name='Tổng tiền NT2'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='t_tt',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng thanh toán', max_digits=18, verbose_name='Tổng thanh toán'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='t_tt_nt',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Tổng thanh toán ngoại tệ', max_digits=18, verbose_name='Tổng TT NT'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='ten_kh_thue',
            field=models.CharField(default='', help_text='Tên khách hàng thuế', max_length=255, verbose_name='Tên KH thuế'),
        ),
        migrations.AlterField(
            model_name='phieunhaphangbantralaimodel',
            name='ty_gia',
            field=models.DecimalField(decimal_places=2, default=1, help_text='Tỷ giá', max_digits=18, verbose_name='Tỷ giá'),
        ),
        migrations.AddIndex(
            model_name='phieunhaphangbantralaimodel',
            index=models.Index(fields=['ma_kh'], name='phieu_nhap__ma_kh_i_094556_idx'),
        ),
        migrations.AddIndex(
            model_name='phieunhaphangbantralaimodel',
            index=models.Index(fields=['ma_nt'], name='phieu_nhap__ma_nt_i_10b0a6_idx'),
        ),
        migrations.AddIndex(
            model_name='phieunhaphangbantralaimodel',
            index=models.Index(fields=['unit_id'], name='phieu_nhap__unit_id_f4ca33_idx'),
        ),
    ]
