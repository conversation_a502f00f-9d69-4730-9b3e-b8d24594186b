"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for PhieuNhapHangBanTraLai (Customer Return Receipt) model.
"""

from typing import Any, Dict, List, Optional, Tuple  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    ChiTietPhieuNhapHangBanTraLaiModel,
    PhieuNhapHangBanTraLaiModel,
)
from django_ledger.repositories.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (  # noqa: F401,
    ChiTietPhieuNhapHangBanTraLaiRepository,
    PhieuNhapHangBanTraLaiRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,


class PhieuNhapHangBanTraLaiService(BaseService):
    """
    Service class for handling PhieuNhapHangBanTraLai business logic.
    Implements the Service pattern for PhieuNhapHangBanTraLai.
    """

    def __init__(self, entity_slug: str = None, user_model=None):  # noqa: F811,
        """
        Initialize the service.

        Parameters
        ----------
        entity_slug : str, optional
            The entity slug, by default None
        user_model : UserModel, optional
            The user model, by default None
        """
        self.repository = PhieuNhapHangBanTraLaiRepository()
        self.chi_tiet_repository = ChiTietPhieuNhapHangBanTraLaiRepository()
        self.entity_slug = entity_slug
        self.user_model = user_model
        super().__init__()

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for PhieuNhapHangBanTraLaiModel.

        Returns
        -------
        QuerySet
            The base queryset
        """
        if self.entity_slug and self.user_model:
            return self.repository.get_for_entity(
                entity_slug=self.entity_slug, user_model=self.user_model
            )
        return self.repository.get_queryset()

    def get(
        self, uuid: UUID
    ) -> Optional[PhieuNhapHangBanTraLaiModel]:  # noqa: C901
        """
        Get a customer return receipt by UUID

        Parameters
        ----------
        uuid : UUID
            The UUID of the customer return receipt

        Returns
        -------
        Optional[PhieuNhapHangBanTraLaiModel]
            The customer return receipt if found, None otherwise
        """
        return self.repository.get_by_uuid(uuid)

    def list(  # noqa: C901
        self,
        search_query: str = None,
        status: str = None,
        from_date: str = None,
        to_date: str = None,
    ) -> QuerySet:
        """
        Get a list of customer return receipts

        Parameters
        ----------
        search_query : str, optional
            Search query to filter results, by default None
        status : str, optional
            Status filter ('1' for active, '0' for inactive), by default None
        from_date : str, optional
            Start date for filtering, by default None
        to_date : str, optional
            End date for filtering, by default None

        Returns
        -------
        QuerySet
            QuerySet of customer return receipts
        """
        if not self.entity_slug or not self.user_model:
            raise ValueError(
                "Entity slug and user model are required for list operation"
            )

        # Get data from repository with filters
        return self.repository.list(
            entity_slug=self.entity_slug,
            user_model=self.user_model,
            search_query=search_query,
            status=status,
            from_date=from_date,
            to_date=to_date,
        )

    @transaction.atomic
    def create(  # noqa: C901
        self,
        data: Dict[str, Any],
        chi_tiet_list: List[Dict[str, Any]] = None,
    ) -> PhieuNhapHangBanTraLaiModel:
        """
        Create a new customer return receipt with optional details

        Parameters
        ----------
        data : Dict[str, Any]
            The data for the new customer return receipt
        chi_tiet_list : List[Dict[str, Any]], optional
            List of details for the customer return receipt, by default None

        Returns
        -------
        PhieuNhapHangBanTraLaiModel
            The created customer return receipt
        """
        if not self.entity_slug or not self.user_model:
            raise ValueError(
                "Entity slug and user model are required for create operation"
            )

        # Create the customer return receipt
        instance = self.repository.create(
            entity_slug=self.entity_slug,
            user_model=self.user_model,
            data=data,
        )

        # Create details if provided
        if chi_tiet_list:
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"Creating {len(chi_tiet_list)} chi_tiet_items for instance {instance.uuid}")

            try:
                self.chi_tiet_repository.create_batch(
                    phieu_nhap_uuid=instance.uuid, details=chi_tiet_list
                )
                logger.info(f"Successfully created chi_tiet_items for instance {instance.uuid}")

                # Update totals only if details were created successfully
                self._update_totals(instance)
            except Exception as e:
                logger.error(f"Error creating chi_tiet_items: {e}")
                logger.error(f"chi_tiet_list data: {chi_tiet_list}")
                # Don't raise to allow the main instance to be created
                # raise  # Re-raise to see the actual error

        return instance

    @transaction.atomic
    def update(
        self, uuid: UUID, data: Dict[str, Any]
    ) -> PhieuNhapHangBanTraLaiModel:  # noqa: C901
        """
        Update a customer return receipt

        Parameters
        ----------
        uuid : UUID
            The UUID of the customer return receipt
        data : Dict[str, Any]
            The data to update the customer return receipt with

        Returns
        -------
        PhieuNhapHangBanTraLaiModel
            The updated customer return receipt
        """
        # Update the customer return receipt
        instance = self.repository.update(uuid=uuid, data=data)
        # Update totals
        self._update_totals(instance)

        return instance

    @transaction.atomic
    def delete(self, uuid: UUID) -> bool:  # noqa: C901
        """
        Delete a customer return receipt

        Parameters
        ----------
        uuid : UUID
            The UUID of the customer return receipt

        Returns
        -------
        bool
            True if the customer return receipt was deleted, False otherwise
        """
        return self.repository.delete(uuid)

    def _update_totals(
        self, instance: PhieuNhapHangBanTraLaiModel
    ) -> None:  # noqa: C901
        """
        Update the totals of a customer return receipt based on its details

        Parameters
        ----------
        instance : PhieuNhapHangBanTraLaiModel
            The customer return receipt to update
        """
        from decimal import Decimal
        import logging

        logger = logging.getLogger(__name__)

        try:
            # Get all details for this customer return receipt
            details = self.chi_tiet_repository.get_by_phieu_nhap(instance.uuid)

            # Helper function to safely convert to Decimal
            def safe_decimal(value):
                if value is None:
                    return Decimal('0')
                try:
                    return Decimal(str(value))
                except (ValueError, TypeError):
                    return Decimal('0')

            # Initialize totals
            t_so_luong = Decimal('0')
            t_tien = Decimal('0')
            t_tien_nt = Decimal('0')
            t_tien2 = Decimal('0')
            t_tien_nt2 = Decimal('0')
            t_ck = Decimal('0')
            t_ck_nt = Decimal('0')
            t_thue = Decimal('0')
            t_thue_nt = Decimal('0')

            # Calculate totals with safe conversion and error handling
            try:
                for detail in details:
                    try:
                        t_so_luong += safe_decimal(detail.so_luong)
                        t_tien += safe_decimal(detail.tien)
                        t_tien_nt += safe_decimal(detail.tien_nt)
                        t_tien2 += safe_decimal(detail.tien2)
                        t_tien_nt2 += safe_decimal(detail.tien_nt2)
                        t_ck += safe_decimal(detail.ck)
                        t_ck_nt += safe_decimal(detail.ck_nt)
                        t_thue += safe_decimal(detail.thue)
                        t_thue_nt += safe_decimal(detail.thue_nt)
                    except Exception as e:
                        logger.warning(f"Error processing detail {detail.uuid}: {e}")
                        continue
            except Exception as e:
                logger.error(f"Error iterating through details: {e}")
                # Set all totals to 0 if there's an error
                t_so_luong = t_tien = t_tien_nt = t_tien2 = t_tien_nt2 = Decimal('0')
                t_ck = t_ck_nt = t_thue = t_thue_nt = Decimal('0')

            # Calculate final totals
            t_tt = t_tien + t_thue - t_ck
            t_tt_nt = t_tien_nt + t_thue_nt - t_ck_nt

            # Update the instance
            instance.t_so_luong = t_so_luong
            instance.t_tien = t_tien
            instance.t_tien_nt = t_tien_nt
            instance.t_tien2 = t_tien2
            instance.t_tien_nt2 = t_tien_nt2
            instance.t_ck = t_ck
            instance.t_ck_nt = t_ck_nt
            instance.t_thue = t_thue
            instance.t_thue_nt = t_thue_nt
            instance.t_tt = t_tt
            instance.t_tt_nt = t_tt_nt

            # Set other totals to 0 for now (can be calculated later if needed)
            instance.t_tc_tien = Decimal('0')
            instance.t_tc_tien_nt2 = Decimal('0')
            instance.t_km = Decimal('0')
            instance.t_km_nt = Decimal('0')

            instance.save(update_fields=[
                't_so_luong', 't_tien', 't_tien_nt', 't_tien2', 't_tien_nt2',
                't_ck', 't_ck_nt', 't_thue', 't_thue_nt', 't_tt', 't_tt_nt',
                't_tc_tien', 't_tc_tien_nt2', 't_km', 't_km_nt'
            ])

        except Exception as e:
            logger.error(f"Error in _update_totals: {e}")
            # Set all totals to 0 if there's a major error
            instance.t_so_luong = Decimal('0')
            instance.t_tien = Decimal('0')
            instance.t_tien_nt = Decimal('0')
            instance.t_tien2 = Decimal('0')
            instance.t_tien_nt2 = Decimal('0')
            instance.t_ck = Decimal('0')
            instance.t_ck_nt = Decimal('0')
            instance.t_thue = Decimal('0')
            instance.t_thue_nt = Decimal('0')
            instance.t_tt = Decimal('0')
            instance.t_tt_nt = Decimal('0')
            instance.t_tc_tien = Decimal('0')
            instance.t_tc_tien_nt2 = Decimal('0')
            instance.t_km = Decimal('0')
            instance.t_km_nt = Decimal('0')

            instance.save(update_fields=[
                't_so_luong', 't_tien', 't_tien_nt', 't_tien2', 't_tien_nt2',
                't_ck', 't_ck_nt', 't_thue', 't_thue_nt', 't_tt', 't_tt_nt',
                't_tc_tien', 't_tc_tien_nt2', 't_km', 't_km_nt'
            ])

    def get_chi_tiet(self, uuid: UUID) -> QuerySet:  # noqa: C901
        """
        Get all details for a customer return receipt

        Parameters
        ----------
        uuid : UUID
            The UUID of the customer return receipt

        Returns
        -------
        QuerySet
            QuerySet of customer return receipt details
        """
        return self.chi_tiet_repository.get_by_phieu_nhap(uuid)

    def execute_raw_query(
        self, query: str, params: List[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Execute raw SQL query through repository.

        This method follows the CCDC allocation pattern for service-to-service communication.

        Parameters
        ----------
        query : str
            The raw SQL query to execute
        params : List[Any], optional
            Query parameters, by default None

        Returns
        -------
        List[Dict[str, Any]]
            Raw query results as list of dictionaries
        """
        return self.repository.execute_raw_query(query, params)
