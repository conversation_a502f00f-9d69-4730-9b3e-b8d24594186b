"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the serializers for the ChiPhiHoaDonMuaHangTrongNuocModel.
"""

from rest_framework import serializers

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (
    ChiPhiHoaDonMuaHangTrongNuocModel,
)


class ChiPhiHoaDonMuaHangTrongNuocModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiPhiHoaDonMuaHangTrongNuocModel.
    Used for read operations.
    """

    # Read-only fields for related objects
    hoa_don_data = serializers.SerializerMethodField(read_only=True)
    ma_cp_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiPhiHoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "hoa_don",
            "hoa_don_data",
            "line",
            "ma_cp",
            "ma_cp_data",
            "tien_cp_nt",
            "tien_cp",
            "ma_kh",
            "ma_kh_data",
            "ten_kh",
            "tk",
            "tk_data",
            "ten_tk",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "hoa_don_data",
            "ma_cp_data",
            "tk_data",
            "ma_kh_data",
            "created",
            "updated",
        ]

    def get_hoa_don_data(self, obj):  # noqa: C901
        """
        Get basic information about the invoice
        """
        if not obj.hoa_don:
            return None

        # Get basic invoice data
        hoa_don_data = {
            "uuid": str(obj.hoa_don.uuid),
            "so_ct": getattr(obj.hoa_don, "so_ct_id", None),
            "ngay_ct": getattr(obj.hoa_don, "ngay_ct", None),
            "ten_kh": getattr(obj.hoa_don, "ten_kh", ""),
        }

        # Add document number if available
        if hasattr(obj.hoa_don, "so_ct") and obj.hoa_don.so_ct:
            hoa_don_data["so_ct_data"] = {
                "uuid": str(obj.hoa_don.so_ct.uuid),
                "ma_ct": getattr(obj.hoa_don.so_ct, "ma_ct", ""),
                "ten_ct": getattr(obj.hoa_don.so_ct, "ten_ct", ""),
            }

        return hoa_don_data

    def get_ma_cp_data(self, obj):  # noqa: C901
        """
        Get basic information about the expense
        """
        if not obj.ma_cp:
            return None

        return {"ma_cp": obj.ma_cp, "ten_cp": getattr(obj, "ten_cp", "")}

    def get_tk_data(self, obj):  # noqa: C901
        """
        Get basic information about the account
        """
        if not obj.tk:
            return None

        return {"code": obj.tk, "name": getattr(obj, "ten_tk", "")}

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Get basic information about the customer
        """
        if not obj.ma_kh:
            return None

        return {"ma_kh": obj.ma_kh, "ten_kh": getattr(obj, "ten_kh", "")}


class ChiPhiHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
    serializers.ModelSerializer
):
    """
    Serializer for the ChiPhiHoaDonMuaHangTrongNuocModel.
    Used for create and update operations.
    """

    class Meta:
        model = ChiPhiHoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "hoa_don",
            "line",
            "ma_cp",
            "tien_cp_nt",
            "tien_cp",
            "ma_kh",
            "tk",
        ]
        read_only_fields = ["uuid"]
        extra_kwargs = {
            'hoa_don': {'required': False},
        }

    def validate(self, data):  # noqa: C901
        """
        Validate the data before creating or updating the model instance.

        Parameters
        ----------
        data : dict
            The data to validate.

        Returns
        -------
        dict
            The validated data.
        """
        # Add any custom validation here
        return data
